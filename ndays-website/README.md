# NDays - National Day Celebrations Website

A modern, SEO-optimized platform for discovering and celebrating national days from around the world. Built with Next.js, TypeScript, and Supabase.

## Features

- 🌍 **Global Coverage**: Celebrations from 195+ countries and territories
- 🎥 **Rich Media**: YouTube integration for authentic celebration videos
- 🔍 **Smart Search**: Advanced filtering by country, date, and category
- 📱 **Responsive Design**: Optimized for all devices
- 🚀 **SEO Optimized**: Dynamic metadata and structured data
- 🔐 **Admin Panel**: Content management and moderation tools
- 🌐 **Geolocation**: Automatic country detection for personalized content

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Deployment**: Vercel/Cloudflare compatible
- **APIs**: YouTube Data API v3, Geolocation API

## Getting Started

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account
- YouTube Data API key (optional for development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ndays-website
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Fill in your environment variables:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key

# Geolocation API Configuration
GEOLOCATION_API_KEY=your_geolocation_api_key

# Next.js Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

4. Set up the database:
```bash
# Run the schema and seed files in your Supabase SQL editor
# Files: database/schema.sql and database/seed.sql
```

5. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── celebrations/      # Celebrations listing and details
│   ├── countries/         # Countries listing and details
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── layout/           # Layout components (Header, Footer)
│   └── ui/               # Reusable UI components
├── lib/                  # Library configurations
│   └── supabase.ts       # Supabase client and helpers
├── types/                # TypeScript type definitions
├── utils/                # Utility functions
└── hooks/                # Custom React hooks

database/
├── schema.sql            # Database schema
└── seed.sql              # Sample data
```

## Database Schema

The application uses the following main tables:

- **countries**: Country information and metadata
- **celebrations**: National day celebrations and events
- **media_content**: YouTube videos and media assets
- **admin_users**: Admin user management
- **content_moderation_actions**: Content approval workflow

## API Integration

### YouTube Data API v3
- Fetches official celebration videos
- Targets verified government and brand channels
- Implements rate limiting and error handling

### Geolocation API
- Detects user's country for personalized content
- Fallback to browser geolocation with user consent
- Handles VPN/proxy scenarios

## Development

### Code Quality
- ESLint and Prettier configured
- TypeScript strict mode enabled
- Husky pre-commit hooks (optional)

### Testing
```bash
npm run test        # Run tests
npm run test:watch  # Run tests in watch mode
npm run test:e2e    # Run end-to-end tests
```

### Building
```bash
npm run build       # Build for production
npm run start       # Start production server
```

## Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Cloudflare Pages
1. Connect repository to Cloudflare Pages
2. Set build command: `npm run build`
3. Set output directory: `.next`
4. Configure environment variables

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or create an issue in the repository.
