# Notion Database Setup Guide for NDays Application

This guide will help you set up the required Notion databases to support the NDays national celebrations application.

## Prerequisites

1. A Notion account (free or paid)
2. Basic understanding of Notion databases and properties

## Step 1: Create a Notion Integration

1. Go to [https://www.notion.so/my-integrations](https://www.notion.so/my-integrations)
2. Click "New integration"
3. Give it a name like "NDays App"
4. Select the workspace where you want to create the databases
5. Click "Submit"
6. Copy the "Internal Integration Token" - you'll need this for your `.env` file

## Step 2: Create the Countries Database

1. Create a new page in Notion
2. Add a database and name it "Countries"
3. Add the following properties:

### Countries Database Properties

| Property Name | Property Type | Description | Required |
|---------------|---------------|-------------|----------|
| Name | Title | Country name (e.g., "United States") | Yes |
| ISO_Code | Rich Text | ISO country code (e.g., "USA") | Yes |
| Description | Rich Text | Brief description of the country | No |
| Capital | Rich Text | Capital city name | No |
| Population | Number | Population count | No |
| Area | Number | Area in square kilometers | No |
| Languages | Multi-select | Languages spoken | No |
| Currency | Rich Text | Currency name/code | No |
| Timezone | Rich Text | Primary timezone | No |
| Flag_URL | URL | URL to flag image | No |

### Sample Countries Data

Add these sample countries to get started:

1. **United States**
   - Name: United States
   - ISO_Code: USA
   - Description: Federal republic in North America
   - Capital: Washington, D.C.
   - Population: 331000000
   - Languages: English
   - Currency: USD

2. **Saudi Arabia**
   - Name: Saudi Arabia
   - ISO_Code: SAU
   - Description: Kingdom in the Middle East
   - Capital: Riyadh
   - Population: 35000000
   - Languages: Arabic
   - Currency: SAR

3. **France**
   - Name: France
   - ISO_Code: FRA
   - Description: Republic in Western Europe
   - Capital: Paris
   - Population: 68000000
   - Languages: French
   - Currency: EUR

## Step 3: Create the Celebrations Database

1. Create another new page in Notion
2. Add a database and name it "Celebrations"
3. Add the following properties:

### Celebrations Database Properties

| Property Name | Property Type | Description | Required |
|---------------|---------------|-------------|----------|
| Title | Title | Celebration name | Yes |
| Description | Rich Text | Detailed description | No |
| Date | Date | Celebration date | Yes |
| Category | Select | Type of celebration | Yes |
| Country | Relation | Link to Countries database | Yes |
| Tags | Multi-select | Relevant tags | No |
| Significance | Rich Text | Historical/cultural significance | No |
| Traditions | Multi-select | Associated traditions | No |

### Category Options

Create these select options for the Category property:
- independence
- cultural
- religious
- historical
- national

### Sample Celebrations Data

Add these sample celebrations:

1. **Independence Day (USA)**
   - Title: Independence Day
   - Description: Celebrating the independence of the United States with fireworks, parades, and patriotic displays.
   - Date: 2024-07-04
   - Category: independence
   - Country: United States
   - Tags: independence, patriotic, fireworks, freedom, history

2. **Saudi National Day**
   - Title: Saudi National Day
   - Description: Commemorating the unification of the Kingdom of Saudi Arabia by King Abdulaziz Al Saud in 1932.
   - Date: 2024-09-23
   - Category: independence
   - Country: Saudi Arabia
   - Tags: national, unification, heritage, patriotic, kingdom

3. **Bastille Day (France)**
   - Title: Bastille Day
   - Description: French national day commemorating the storming of the Bastille fortress in 1789.
   - Date: 2024-07-14
   - Category: independence
   - Country: France
   - Tags: revolution, freedom, national, liberty, history

## Step 4: Create the Media Database

1. Create a third new page in Notion
2. Add a database and name it "Media"
3. Add the following properties:

### Media Database Properties

| Property Name | Property Type | Description | Required |
|---------------|---------------|-------------|----------|
| Title | Title | Media title | Yes |
| Description | Rich Text | Media description | No |
| Type | Select | Media type | Yes |
| URL | URL | Media URL | Yes |
| Thumbnail_URL | URL | Thumbnail image URL | No |
| Duration | Number | Duration in seconds (for videos) | No |
| Celebration | Relation | Link to Celebrations database | Yes |

### Type Options

Create these select options for the Type property:
- youtube
- image
- video

### Sample Media Data

Add sample media content:

1. **Independence Day Fireworks**
   - Title: Fourth of July Fireworks Display
   - Description: Spectacular fireworks display over Washington D.C.
   - Type: youtube
   - URL: https://www.youtube.com/watch?v=dQw4w9WgXcQ
   - Celebration: Independence Day

## Step 5: Share Databases with Integration

1. For each database, click the "Share" button in the top right
2. Click "Invite" and search for your integration name
3. Give it "Edit" permissions
4. Click "Invite"

## Step 6: Get Database IDs

1. For each database, copy the URL from your browser
2. The database ID is the long string of characters after the last slash and before any query parameters
3. Example: `https://www.notion.so/myworkspace/Countries-abc123def456?v=...`
   - Database ID: `abc123def456`

## Step 7: Update Environment Variables

Update your `.env` file with the following:

```bash
# Notion API Configuration
NOTION_API_TOKEN=your_integration_token_here
NOTION_COUNTRIES_DATABASE_ID=your_countries_database_id_here
NOTION_CELEBRATIONS_DATABASE_ID=your_celebrations_database_id_here
NOTION_MEDIA_DATABASE_ID=your_media_database_id_here
```

## Step 8: Test the Integration

1. Restart your development server: `npm run dev`
2. Visit `http://localhost:3000/countries` to see if countries are loading
3. Visit `http://localhost:3000/celebrations` to see if celebrations are loading

## Troubleshooting

### Common Issues

1. **"Unauthorized" errors**: Make sure you've shared all databases with your integration
2. **"Database not found" errors**: Double-check your database IDs in the `.env` file
3. **Empty data**: Ensure you've added sample data to your databases
4. **Property errors**: Make sure property names match exactly (case-sensitive)

### Debugging Tips

1. Check the browser console for error messages
2. Verify your integration token is correct
3. Ensure all required properties are filled in your Notion databases
4. Test the Notion API directly using their API explorer

## Next Steps

Once your Notion integration is working:

1. Add more countries and celebrations to your databases
2. Upload media content and link it to celebrations
3. Customize the database properties to match your specific needs
4. Consider adding more detailed information like flag meanings, cultural context, etc.

## Database Schema Summary

```
Countries
├── Name (Title)
├── ISO_Code (Rich Text)
├── Description (Rich Text)
├── Capital (Rich Text)
├── Population (Number)
├── Area (Number)
├── Languages (Multi-select)
├── Currency (Rich Text)
├── Timezone (Rich Text)
└── Flag_URL (URL)

Celebrations
├── Title (Title)
├── Description (Rich Text)
├── Date (Date)
├── Category (Select: independence|cultural|religious|historical|national)
├── Country (Relation → Countries)
├── Tags (Multi-select)
├── Significance (Rich Text)
└── Traditions (Multi-select)

Media
├── Title (Title)
├── Description (Rich Text)
├── Type (Select: youtube|image|video)
├── URL (URL)
├── Thumbnail_URL (URL)
├── Duration (Number)
└── Celebration (Relation → Celebrations)
```

This setup will provide a solid foundation for your NDays application with Notion as the backend data source.
