const { Client } = require('@notionhq/client');
require('dotenv').config();

async function testNotionConnection() {
  try {
    console.log('Testing Notion API connection...');
    console.log('API Token:', process.env.NOTION_API_TOKEN ? 'Set' : 'Not set');
    console.log('API Token value:', process.env.NOTION_API_TOKEN);
    console.log('Countries DB ID:', process.env.NOTION_COUNTRIES_DATABASE_ID ? 'Set' : 'Not set');
    console.log('Countries DB ID value:', process.env.NOTION_COUNTRIES_DATABASE_ID);
    console.log('All env vars:', Object.keys(process.env).filter(key => key.startsWith('NOTION')));

    const notion = new Client({
      auth: process.env.NOTION_API_TOKEN,
      notionVersion: '2022-06-28', // Use older API version that supports databases.query
    });

    console.log('Notion client created successfully');
    console.log('Available methods on notion:', Object.getOwnPropertyNames(notion));
    console.log('Available methods on databases:', Object.getOwnPropertyNames(notion.databases));

    // Test basic connection - check if query method exists on pages
    console.log('Database ID being used:', process.env.NOTION_COUNTRIES_DATABASE_ID);

    // Let's try the search API instead
    try {
      console.log('Trying search API for data sources...');
      const response = await notion.search({
        query: '',
        filter: {
          value: 'data_source',
          property: 'object'
        },
        page_size: 10
      });

      console.log('✅ Search successful!');
      console.log('Found databases:', response.results.length);

      // Look for our database
      const ourDatabase = response.results.find(db => db.id.replace(/-/g, '') === process.env.NOTION_COUNTRIES_DATABASE_ID.replace(/-/g, ''));
      if (ourDatabase) {
        console.log('✅ Found our database!');
        console.log('Database title:', ourDatabase.title?.[0]?.plain_text || 'No title');
      } else {
        console.log('❌ Our database not found in search results');
        console.log('Available databases:');
        response.results.forEach((db, i) => {
          console.log(`${i + 1}. ID: ${db.id}, Title: ${db.title?.[0]?.plain_text || 'No title'}`);
        });
      }

      // Try to access the database directly
      console.log('\nTrying to retrieve database directly...');
      try {
        const dbResponse = await notion.databases.retrieve({
          database_id: process.env.NOTION_COUNTRIES_DATABASE_ID
        });
        console.log('✅ Database retrieved successfully!');
        console.log('Database title:', dbResponse.title?.[0]?.plain_text || 'No title');
        console.log('Database properties:', Object.keys(dbResponse.properties || {}));

        // Now try to query the database
        console.log('\nTrying to query database...');
        if (notion.databases.query) {
          const queryResponse = await notion.databases.query({
            database_id: process.env.NOTION_COUNTRIES_DATABASE_ID,
            page_size: 5
          });
          console.log('✅ Database query successful!');
          console.log('Found pages:', queryResponse.results.length);
          if (queryResponse.results.length > 0) {
            console.log('First page properties:', Object.keys(queryResponse.results[0].properties || {}));
          }
        } else {
          console.log('❌ databases.query method not available');
        }

      } catch (dbError) {
        console.log('❌ Direct database retrieval failed:', dbError.message);
      }

    } catch (error) {
      console.log('Search failed. Error details:');
      console.log('Error:', error.message);
      throw error;
    }
    
    console.log('✅ Connection successful!');
    console.log('Number of countries found:', response.results.length);
    
    if (response.results.length > 0) {
      console.log('\nFirst country data:');
      const firstCountry = response.results[0];
      console.log('ID:', firstCountry.id);
      console.log('Properties:', Object.keys(firstCountry.properties));
      
      // Try to extract name
      const nameProperty = firstCountry.properties.Name || firstCountry.properties.name;
      if (nameProperty && nameProperty.title) {
        console.log('Name:', nameProperty.title[0]?.plain_text || 'No name found');
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing Notion connection:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.code === 'unauthorized') {
      console.error('\n🔑 Authorization issue:');
      console.error('- Check if your Notion API token is correct');
      console.error('- Make sure the integration has access to the database');
      console.error('- Verify the database ID is correct');
    }
    
    if (error.code === 'object_not_found') {
      console.error('\n🔍 Database not found:');
      console.error('- Check if the database ID is correct');
      console.error('- Make sure the integration has access to the database');
    }
  }
}

testNotionConnection();
