{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/utils/cn';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,+LAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,iNAAC;QACC,WAAW,IAAA,+IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils/cn';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,+LAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,iNAAC;QACC,MAAM;QACN,WAAW,IAAA,+IAAE,EACX,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Search, Menu, X, Globe } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { cn } from '@/utils/cn';\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Navigate to search results page\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Globe className=\"h-6 w-6 text-primary\" />\n            <span className=\"text-xl font-bold\">NDays</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            <Link\n              href=\"/\"\n              className=\"text-sm font-medium transition-colors hover:text-primary\"\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/countries\"\n              className=\"text-sm font-medium transition-colors hover:text-primary\"\n            >\n              Countries\n            </Link>\n            <Link\n              href=\"/celebrations\"\n              className=\"text-sm font-medium transition-colors hover:text-primary\"\n            >\n              Celebrations\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-sm font-medium transition-colors hover:text-primary\"\n            >\n              About\n            </Link>\n          </nav>\n\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"hidden md:flex items-center space-x-2\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search celebrations...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-8 w-64\"\n              />\n            </div>\n            <Button type=\"submit\" size=\"sm\">\n              Search\n            </Button>\n          </form>\n\n          {/* Mobile Menu Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n          </Button>\n        </div>\n\n        {/* Mobile Menu */}\n        <div\n          className={cn(\n            'md:hidden overflow-hidden transition-all duration-300 ease-in-out',\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          )}\n        >\n          <div className=\"py-4 space-y-4\">\n            <nav className=\"flex flex-col space-y-2\">\n              <Link\n                href=\"/\"\n                className=\"text-sm font-medium transition-colors hover:text-primary py-2\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/countries\"\n                className=\"text-sm font-medium transition-colors hover:text-primary py-2\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Countries\n              </Link>\n              <Link\n                href=\"/celebrations\"\n                className=\"text-sm font-medium transition-colors hover:text-primary py-2\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Celebrations\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-sm font-medium transition-colors hover:text-primary py-2\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                About\n              </Link>\n            </nav>\n\n            {/* Mobile Search */}\n            <form onSubmit={handleSearch} className=\"flex items-center space-x-2\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Search celebrations...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-8\"\n                />\n              </div>\n              <Button type=\"submit\" size=\"sm\">\n                Search\n              </Button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,6LAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,6LAAQ,EAAC;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,aAA4C,OAAhC,mBAAmB;QACzD;IACF;IAEA,qBACE,iNAAC;QAAO,WAAU;kBAChB,cAAA,iNAAC;YAAI,WAAU;;8BACb,iNAAC;oBAAI,WAAU;;sCAEb,iNAAC,8LAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,iNAAC,gNAAK;oCAAC,WAAU;;;;;;8CACjB,iNAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAItC,iNAAC;4BAAI,WAAU;;8CACb,iNAAC,8LAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,iNAAC,8LAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,iNAAC,8LAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,iNAAC,8LAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,iNAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,iNAAC;oCAAI,WAAU;;sDACb,iNAAC,mNAAM;4CAAC,WAAU;;;;;;sDAClB,iNAAC,iKAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAGd,iNAAC,mKAAM;oCAAC,MAAK;oCAAS,MAAK;8CAAK;;;;;;;;;;;;sCAMlC,iNAAC,mKAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BAAa,iNAAC,oMAAC;gCAAC,WAAU;;;;;qDAAe,iNAAC,6MAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK9D,iNAAC;oBACC,WAAW,IAAA,+IAAE,EACX,qEACA,aAAa,yBAAyB;8BAGxC,cAAA,iNAAC;wBAAI,WAAU;;0CACb,iNAAC;gCAAI,WAAU;;kDACb,iNAAC,8LAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,iNAAC,8LAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,iNAAC,8LAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,iNAAC,8LAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;0CAMH,iNAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,iNAAC;wCAAI,WAAU;;0DACb,iNAAC,mNAAM;gDAAC,WAAU;;;;;;0DAClB,iNAAC,iKAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAGd,iNAAC,mKAAM;wCAAC,MAAK;wCAAS,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GAxIgB;KAAA", "debugId": null}}]}