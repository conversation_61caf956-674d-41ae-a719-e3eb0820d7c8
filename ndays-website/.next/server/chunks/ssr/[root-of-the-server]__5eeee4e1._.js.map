{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/utils/cn';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,uOAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,kQAAC;QACC,WAAW,IAAA,4IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils/cn';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,uOAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QACC,KAAK;QACL,WAAW,IAAA,4IAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,uOAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4IAAE,EAAC,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,uOAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QACC,KAAK;QACL,WAAW,IAAA,4IAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,uOAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QACC,KAAK;QACL,WAAW,IAAA,4IAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,uOAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4IAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,uOAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kQAAC;QACC,KAAK;QACL,WAAW,IAAA,4IAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/MyProjs/ndays/ndays-website/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Calendar, Globe, Search, TrendingUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\n\nexport default function Home() {\n  return (\n    <div className=\"flex flex-col\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20 lg:py-32\">\n        <div className=\"container\">\n          <div className=\"mx-auto max-w-4xl text-center\">\n            <h1 className=\"text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl\">\n              Discover National Days\n              <span className=\"text-primary\"> Around the World</span>\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-muted-foreground sm:text-xl\">\n              Explore authentic celebrations, cultural traditions, and national holidays from every country.\n              Connect with the world through verified content and official celebrations.\n            </p>\n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <Button size=\"lg\" asChild>\n                <Link href=\"/celebrations\">\n                  <Search className=\"mr-2 h-4 w-4\" />\n                  Explore Celebrations\n                </Link>\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" asChild>\n                <Link href=\"/countries\">\n                  <Globe className=\"mr-2 h-4 w-4\" />\n                  Browse Countries\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 lg:py-32\">\n        <div className=\"container\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight sm:text-4xl\">\n              Everything you need to celebrate\n            </h2>\n            <p className=\"mt-4 text-lg text-muted-foreground\">\n              From independence days to cultural festivals, discover the stories behind every celebration.\n            </p>\n          </div>\n          <div className=\"mx-auto mt-16 max-w-5xl\">\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              <Card>\n                <CardHeader>\n                  <Calendar className=\"h-8 w-8 text-primary\" />\n                  <CardTitle>Authentic Content</CardTitle>\n                  <CardDescription>\n                    Curated content from official sources and verified channels\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Every celebration is backed by authentic videos, images, and information\n                    from government sources and major cultural institutions.\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <Globe className=\"h-8 w-8 text-primary\" />\n                  <CardTitle>Global Coverage</CardTitle>\n                  <CardDescription>\n                    Celebrations from every country and territory\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Comprehensive database covering national days, independence celebrations,\n                    and cultural festivals from all 195 countries.\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <Search className=\"h-8 w-8 text-primary\" />\n                  <CardTitle>Smart Discovery</CardTitle>\n                  <CardDescription>\n                    Find celebrations by country, date, or category\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Advanced search and filtering options help you discover celebrations\n                    that matter to you, with personalized recommendations.\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"bg-muted/50 py-20 lg:py-32\">\n        <div className=\"container\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight sm:text-4xl\">\n              Trusted by celebration enthusiasts worldwide\n            </h2>\n          </div>\n          <div className=\"mx-auto mt-16 max-w-5xl\">\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-3\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-primary\">195+</div>\n                <div className=\"mt-2 text-sm text-muted-foreground\">Countries Covered</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-primary\">1000+</div>\n                <div className=\"mt-2 text-sm text-muted-foreground\">National Celebrations</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-primary\">5000+</div>\n                <div className=\"mt-2 text-sm text-muted-foreground\">Authentic Videos</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 lg:py-32\">\n        <div className=\"container\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight sm:text-4xl\">\n              Start exploring today\n            </h2>\n            <p className=\"mt-4 text-lg text-muted-foreground\">\n              Join thousands of users discovering the rich cultural heritage of our world.\n            </p>\n            <div className=\"mt-8\">\n              <Button size=\"lg\" asChild>\n                <Link href=\"/celebrations\">\n                  <TrendingUp className=\"mr-2 h-4 w-4\" />\n                  Get Started\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,kQAAC;QAAI,WAAU;;0BAEb,kQAAC;gBAAQ,WAAU;0BACjB,cAAA,kQAAC;oBAAI,WAAU;8BACb,cAAA,kQAAC;wBAAI,WAAU;;0CACb,kQAAC;gCAAG,WAAU;;oCAA4D;kDAExE,kQAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjC,kQAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAIvE,kQAAC;gCAAI,WAAU;;kDACb,kQAAC,gKAAM;wCAAC,MAAK;wCAAK,OAAO;kDACvB,cAAA,kQAAC,2LAAI;4CAAC,MAAK;;8DACT,kQAAC,gNAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIvC,kQAAC,gKAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,OAAO;kDACzC,cAAA,kQAAC,2LAAI;4CAAC,MAAK;;8DACT,kQAAC,6MAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9C,kQAAC;gBAAQ,WAAU;0BACjB,cAAA,kQAAC;oBAAI,WAAU;;sCACb,kQAAC;4BAAI,WAAU;;8CACb,kQAAC;oCAAG,WAAU;8CAAgD;;;;;;8CAG9D,kQAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,kQAAC;4BAAI,WAAU;sCACb,cAAA,kQAAC;gCAAI,WAAU;;kDACb,kQAAC,4JAAI;;0DACH,kQAAC,kKAAU;;kEACT,kQAAC,sNAAQ;wDAAC,WAAU;;;;;;kEACpB,kQAAC,iKAAS;kEAAC;;;;;;kEACX,kQAAC,uKAAe;kEAAC;;;;;;;;;;;;0DAInB,kQAAC,mKAAW;0DACV,cAAA,kQAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAOjD,kQAAC,4JAAI;;0DACH,kQAAC,kKAAU;;kEACT,kQAAC,6MAAK;wDAAC,WAAU;;;;;;kEACjB,kQAAC,iKAAS;kEAAC;;;;;;kEACX,kQAAC,uKAAe;kEAAC;;;;;;;;;;;;0DAInB,kQAAC,mKAAW;0DACV,cAAA,kQAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAOjD,kQAAC,4JAAI;;0DACH,kQAAC,kKAAU;;kEACT,kQAAC,gNAAM;wDAAC,WAAU;;;;;;kEAClB,kQAAC,iKAAS;kEAAC;;;;;;kEACX,kQAAC,uKAAe;kEAAC;;;;;;;;;;;;0DAInB,kQAAC,mKAAW;0DACV,cAAA,kQAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYzD,kQAAC;gBAAQ,WAAU;0BACjB,cAAA,kQAAC;oBAAI,WAAU;;sCACb,kQAAC;4BAAI,WAAU;sCACb,cAAA,kQAAC;gCAAG,WAAU;0CAAgD;;;;;;;;;;;sCAIhE,kQAAC;4BAAI,WAAU;sCACb,cAAA,kQAAC;gCAAI,WAAU;;kDACb,kQAAC;wCAAI,WAAU;;0DACb,kQAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,kQAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAEtD,kQAAC;wCAAI,WAAU;;0DACb,kQAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,kQAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;kDAEtD,kQAAC;wCAAI,WAAU;;0DACb,kQAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,kQAAC;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,kQAAC;gBAAQ,WAAU;0BACjB,cAAA,kQAAC;oBAAI,WAAU;8BACb,cAAA,kQAAC;wBAAI,WAAU;;0CACb,kQAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,kQAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,kQAAC;gCAAI,WAAU;0CACb,cAAA,kQAAC,gKAAM;oCAAC,MAAK;oCAAK,OAAO;8CACvB,cAAA,kQAAC,2LAAI;wCAAC,MAAK;;0DACT,kQAAC,gOAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}]}