var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/_not-found/page.js")
R.c("server/chunks/ssr/386a4_25b0efbd._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/ndays-website_src_app_dcaba12b._.js")
R.c("server/chunks/ssr/[root-of-the-server]__6b3a39fe._.js")
R.c("server/chunks/ssr/386a4_next_dist_client_components_2046f002._.js")
R.c("server/chunks/ssr/386a4_next_dist_client_components_builtin_forbidden_607418e9.js")
R.c("server/chunks/ssr/386a4_next_dist_36b4acd2._.js")
R.c("server/chunks/ssr/[root-of-the-server]__eb28e757._.js")
R.m("[project]/ndays-website/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/ndays-website/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/ndays-website/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/ndays-website/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/ndays-website/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/ndays-website/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/ndays-website/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/ndays-website/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/ndays-website/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/ndays-website/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
