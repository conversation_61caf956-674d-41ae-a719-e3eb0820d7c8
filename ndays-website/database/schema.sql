-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Countries table
CREATE TABLE countries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    iso_code VARCHAR(3) NOT NULL UNIQUE,
    flag_url TEXT,
    timezone VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Celebrations table
CREATE TABLE celebrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    country_id UUID NOT NULL REFERENCES countries(id) ON DELETE CASCADE,
    tags TEXT[] DEFAULT '{}',
    category VARCHAR(100),
    approval_status VARCHAR(20) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media content table
CREATE TABLE media_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('youtube', 'image', 'video')),
    source VARCHAR(255) NOT NULL,
    attribution TEXT,
    celebration_id UUID NOT NULL REFERENCES celebrations(id) ON DELETE CASCADE,
    thumbnail_url TEXT,
    title VARCHAR(500),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin users table
CREATE TABLE admin_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    role VARCHAR(20) DEFAULT 'moderator' CHECK (role IN ('admin', 'moderator')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content moderation actions table
CREATE TABLE content_moderation_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    celebration_id UUID NOT NULL REFERENCES celebrations(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES admin_users(id) ON DELETE CASCADE,
    action VARCHAR(20) NOT NULL CHECK (action IN ('approve', 'reject', 'edit')),
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_celebrations_country_id ON celebrations(country_id);
CREATE INDEX idx_celebrations_date ON celebrations(date);
CREATE INDEX idx_celebrations_approval_status ON celebrations(approval_status);
CREATE INDEX idx_celebrations_category ON celebrations(category);
CREATE INDEX idx_media_content_celebration_id ON media_content(celebration_id);
CREATE INDEX idx_media_content_type ON media_content(type);
CREATE INDEX idx_content_moderation_celebration_id ON content_moderation_actions(celebration_id);
CREATE INDEX idx_content_moderation_admin_id ON content_moderation_actions(admin_id);

-- Full-text search indexes
CREATE INDEX idx_celebrations_title_search ON celebrations USING gin(to_tsvector('english', title));
CREATE INDEX idx_celebrations_description_search ON celebrations USING gin(to_tsvector('english', description));

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_countries_updated_at BEFORE UPDATE ON countries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_celebrations_updated_at BEFORE UPDATE ON celebrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_media_content_updated_at BEFORE UPDATE ON media_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE countries ENABLE ROW LEVEL SECURITY;
ALTER TABLE celebrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_moderation_actions ENABLE ROW LEVEL SECURITY;

-- Public read access for approved content
CREATE POLICY "Public read access for countries" ON countries FOR SELECT USING (true);
CREATE POLICY "Public read access for approved celebrations" ON celebrations FOR SELECT USING (approval_status = 'approved');
CREATE POLICY "Public read access for media content" ON media_content FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM celebrations 
        WHERE celebrations.id = media_content.celebration_id 
        AND celebrations.approval_status = 'approved'
    )
);

-- Admin access policies (to be configured with authentication)
CREATE POLICY "Admin full access to countries" ON countries FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE admin_users.email = auth.email()
    )
);

CREATE POLICY "Admin full access to celebrations" ON celebrations FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE admin_users.email = auth.email()
    )
);

CREATE POLICY "Admin full access to media content" ON media_content FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE admin_users.email = auth.email()
    )
);

CREATE POLICY "Admin read access to admin users" ON admin_users FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE admin_users.email = auth.email()
    )
);

CREATE POLICY "Admin full access to moderation actions" ON content_moderation_actions FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE admin_users.email = auth.email()
    )
);
