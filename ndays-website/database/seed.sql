-- Insert sample countries
INSERT INTO countries (name, iso_code, timezone) VALUES
('United States', 'USA', 'America/New_York'),
('United Kingdom', 'GBR', 'Europe/London'),
('France', 'FRA', 'Europe/Paris'),
('Germany', 'DEU', 'Europe/Berlin'),
('Japan', 'JPN', 'Asia/Tokyo'),
('Australia', 'AUS', 'Australia/Sydney'),
('Canada', 'CAN', 'America/Toronto'),
('India', 'IND', 'Asia/Kolkata'),
('Brazil', 'BRA', 'America/Sao_Paulo'),
('Mexico', 'MEX', 'America/Mexico_City'),
('Italy', 'ITA', 'Europe/Rome'),
('Spain', 'ESP', 'Europe/Madrid'),
('China', 'CHN', 'Asia/Shanghai'),
('South Korea', 'KOR', 'Asia/Seoul'),
('Netherlands', 'NLD', 'Europe/Amsterdam'),
('Sweden', 'SWE', 'Europe/Stockholm'),
('Norway', 'NOR', 'Europe/Oslo'),
('Denmark', 'DNK', 'Europe/Copenhagen'),
('Finland', 'FIN', 'Europe/Helsinki'),
('Switzerland', 'CHE', 'Europe/Zurich');

-- Insert sample celebrations (using country IDs - these will need to be updated with actual UUIDs)
-- Note: In a real implementation, you would get the actual UUIDs from the countries table

-- Sample celebrations for demonstration
INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Independence Day',
    'Celebrating the independence of the United States with fireworks, parades, and patriotic displays.',
    '2024-07-04',
    c.id,
    ARRAY['independence', 'patriotic', 'fireworks'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'USA';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Bastille Day',
    'French national day commemorating the storming of the Bastille fortress in 1789.',
    '2024-07-14',
    c.id,
    ARRAY['revolution', 'freedom', 'national'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'FRA';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Canada Day',
    'Celebrating the anniversary of Canadian Confederation with festivals and fireworks.',
    '2024-07-01',
    c.id,
    ARRAY['confederation', 'national', 'celebration'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'CAN';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Australia Day',
    'National day of Australia commemorating the arrival of the First Fleet.',
    '2024-01-26',
    c.id,
    ARRAY['national', 'heritage', 'celebration'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'AUS';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'German Unity Day',
    'Commemorating the reunification of Germany in 1990.',
    '2024-10-03',
    c.id,
    ARRAY['unity', 'reunification', 'national'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'DEU';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Constitution Day',
    'Celebrating the Norwegian Constitution signed in 1814.',
    '2024-05-17',
    c.id,
    ARRAY['constitution', 'national', 'democracy'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'NOR';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Republic Day',
    'Celebrating the adoption of the Indian Constitution.',
    '2024-01-26',
    c.id,
    ARRAY['republic', 'constitution', 'democracy'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'IND';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Independence Day',
    'Celebrating Brazilian independence from Portugal.',
    '2024-09-07',
    c.id,
    ARRAY['independence', 'national', 'freedom'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'BRA';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'National Day',
    'Celebrating the founding of the People''s Republic of China.',
    '2024-10-01',
    c.id,
    ARRAY['national', 'founding', 'republic'],
    'independence',
    'approved'
FROM countries c WHERE c.iso_code = 'CHN';

INSERT INTO celebrations (title, description, date, country_id, tags, category, approval_status) 
SELECT 
    'Liberation Day',
    'Commemorating the liberation of the Netherlands in World War II.',
    '2024-05-05',
    c.id,
    ARRAY['liberation', 'freedom', 'remembrance'],
    'historical',
    'approved'
FROM countries c WHERE c.iso_code = 'NLD';

-- Insert sample admin user
INSERT INTO admin_users (email, role) VALUES
('<EMAIL>', 'admin');

-- Note: Media content would be added through the YouTube API integration
-- This is just the basic structure for demonstration
