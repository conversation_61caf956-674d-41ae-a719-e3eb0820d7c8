'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { GeolocationResponse } from '@/types';
import { getUserCountry, mapCountryCodeToId } from '@/utils/geolocation';

interface GeolocationContextType {
  location: GeolocationResponse | null;
  isLoading: boolean;
  error: string | null;
  countryId: string;
  refetch: () => Promise<void>;
}

const GeolocationContext = createContext<GeolocationContextType | undefined>(undefined);

export function useGeolocation() {
  const context = useContext(GeolocationContext);
  if (context === undefined) {
    throw new Error('useGeolocation must be used within a GeolocationProvider');
  }
  return context;
}

interface GeolocationProviderProps {
  children: React.ReactNode;
}

export function GeolocationProvider({ children }: GeolocationProviderProps) {
  const [location, setLocation] = useState<GeolocationResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLocation = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await getUserCountry();
      setLocation(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get location');
      // Set fallback location
      setLocation({
        country: 'United States',
        countryCode: 'US',
        timezone: 'America/New_York',
        success: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLocation();
  }, []);

  const countryId = location ? mapCountryCodeToId(location.countryCode) : 'usa';

  const value: GeolocationContextType = {
    location,
    isLoading,
    error,
    countryId,
    refetch: fetchLocation,
  };

  return (
    <GeolocationContext.Provider value={value}>
      {children}
    </GeolocationContext.Provider>
  );
}
