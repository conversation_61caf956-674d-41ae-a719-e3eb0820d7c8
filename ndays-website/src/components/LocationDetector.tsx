'use client';

import React from 'react';
import Link from 'next/link';
import { MapPin, RefreshCw, AlertCircle, ExternalLink } from 'lucide-react';
import { useGeolocation } from './GeolocationProvider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LocationDetectorProps {
  showCard?: boolean;
  className?: string;
}

export function LocationDetector({ showCard = true, className = '' }: LocationDetectorProps) {
  const { location, isLoading, error, countryId, refetch } = useGeolocation();

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 text-muted-foreground ${className}`}>
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span className="text-sm">Detecting your location...</span>
      </div>
    );
  }

  if (error && !location) {
    return (
      <Alert className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Unable to detect your location. Showing default content.
          <Button variant="link" onClick={refetch} className="p-0 h-auto ml-2">
            Try again
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!location) {
    return null;
  }

  const content = (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-primary" />
          <div>
            <div className="font-medium">{location.country}</div>
            <div className="text-sm text-muted-foreground">
              {location.success ? 'Auto-detected' : 'Default location'}
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={refetch}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/countries/${countryId}`}>
            <ExternalLink className="h-4 w-4 mr-1" />
            View Celebrations
          </Link>
        </Button>
      </div>
    </div>
  );

  if (!showCard) {
    return <div className={className}>{content}</div>;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Your Location</CardTitle>
        <CardDescription>
          We've detected your location to show relevant national celebrations
        </CardDescription>
      </CardHeader>
      <CardContent>{content}</CardContent>
    </Card>
  );
}

export function LocationBadge({ className = '' }: { className?: string }) {
  const { location, isLoading } = useGeolocation();

  if (isLoading) {
    return (
      <div className={`inline-flex items-center space-x-1 text-sm text-muted-foreground ${className}`}>
        <RefreshCw className="h-3 w-3 animate-spin" />
        <span>Detecting...</span>
      </div>
    );
  }

  if (!location) {
    return null;
  }

  return (
    <div className={`inline-flex items-center space-x-1 text-sm ${className}`}>
      <MapPin className="h-3 w-3 text-primary" />
      <span>{location.country}</span>
    </div>
  );
}
