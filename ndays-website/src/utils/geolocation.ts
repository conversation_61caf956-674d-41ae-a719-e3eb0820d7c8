import { GeolocationResponse } from '@/types';

/**
 * Get user's country using IP-based geolocation
 */
export async function getCountryByIP(): Promise<GeolocationResponse> {
  try {
    // Try multiple IP geolocation services for better reliability
    const services = [
      'https://ipapi.co/json/',
      'https://ip-api.com/json/',
      'https://ipinfo.io/json',
    ];

    for (const service of services) {
      try {
        const response = await fetch(service);
        if (!response.ok) continue;
        
        const data = await response.json();
        
        // Handle different API response formats
        let country = '';
        let countryCode = '';
        let timezone = '';

        if (service.includes('ipapi.co')) {
          country = data.country_name || '';
          countryCode = data.country_code || '';
          timezone = data.timezone || '';
        } else if (service.includes('ip-api.com')) {
          country = data.country || '';
          countryCode = data.countryCode || '';
          timezone = data.timezone || '';
        } else if (service.includes('ipinfo.io')) {
          country = data.country || '';
          countryCode = data.country || '';
          timezone = data.timezone || '';
        }

        if (country && countryCode) {
          return {
            country,
            countryCode,
            timezone,
            success: true,
          };
        }
      } catch (error) {
        console.warn(`Geolocation service ${service} failed:`, error);
        continue;
      }
    }

    throw new Error('All geolocation services failed');
  } catch (error) {
    console.error('IP geolocation failed:', error);
    return {
      country: 'Unknown',
      countryCode: 'XX',
      timezone: 'UTC',
      success: false,
    };
  }
}

/**
 * Get user's location using browser geolocation API
 */
export function getBrowserLocation(): Promise<GeolocationResponse> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      resolve({
        country: 'Unknown',
        countryCode: 'XX',
        timezone: 'UTC',
        success: false,
      });
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // Use reverse geocoding to get country from coordinates
          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
          );
          
          if (!response.ok) {
            throw new Error('Reverse geocoding failed');
          }
          
          const data = await response.json();
          
          resolve({
            country: data.countryName || 'Unknown',
            countryCode: data.countryCode || 'XX',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
            success: true,
          });
        } catch (error) {
          console.error('Browser geolocation reverse geocoding failed:', error);
          resolve({
            country: 'Unknown',
            countryCode: 'XX',
            timezone: 'UTC',
            success: false,
          });
        }
      },
      (error) => {
        console.error('Browser geolocation failed:', error);
        resolve({
          country: 'Unknown',
          countryCode: 'XX',
          timezone: 'UTC',
          success: false,
        });
      },
      {
        timeout: 10000,
        enableHighAccuracy: false,
      }
    );
  });
}

/**
 * Get user's country with fallback strategy
 * 1. Try IP-based geolocation first (faster, no permission required)
 * 2. Fall back to browser geolocation if IP fails
 * 3. Return default if both fail
 */
export async function getUserCountry(): Promise<GeolocationResponse> {
  // First try IP-based geolocation
  const ipResult = await getCountryByIP();
  if (ipResult.success) {
    return ipResult;
  }

  // Fall back to browser geolocation
  const browserResult = await getBrowserLocation();
  if (browserResult.success) {
    return browserResult;
  }

  // Return default fallback
  return {
    country: 'United States',
    countryCode: 'US',
    timezone: 'America/New_York',
    success: false,
  };
}

/**
 * Map country codes to our internal country identifiers
 */
export function mapCountryCodeToId(countryCode: string): string {
  const countryMap: Record<string, string> = {
    'US': 'usa',
    'USA': 'usa',
    'FR': 'fra',
    'FRA': 'fra',
    'CA': 'can',
    'CAN': 'can',
    'GB': 'gbr',
    'UK': 'gbr',
    'GBR': 'gbr',
    'DE': 'deu',
    'DEU': 'deu',
    'JP': 'jpn',
    'JPN': 'jpn',
    'AU': 'aus',
    'AUS': 'aus',
    'IN': 'ind',
    'IND': 'ind',
    'BR': 'bra',
    'BRA': 'bra',
    'MX': 'mex',
    'MEX': 'mex',
    'IT': 'ita',
    'ITA': 'ita',
    'ES': 'esp',
    'ESP': 'esp',
    'CN': 'chn',
    'CHN': 'chn',
    'KR': 'kor',
    'KOR': 'kor',
    'NL': 'nld',
    'NLD': 'nld',
    'SE': 'swe',
    'SWE': 'swe',
    'NO': 'nor',
    'NOR': 'nor',
    'DK': 'dnk',
    'DNK': 'dnk',
    'FI': 'fin',
    'FIN': 'fin',
    'CH': 'che',
    'CHE': 'che',
  };

  return countryMap[countryCode.toUpperCase()] || 'usa';
}

/**
 * Check if geolocation is supported in the current environment
 */
export function isGeolocationSupported(): boolean {
  return typeof navigator !== 'undefined' && 'geolocation' in navigator;
}
