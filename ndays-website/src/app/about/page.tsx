import { Metadata } from 'next';
import { Globe, Users, Shield, Heart } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'About NDays',
  description: 'Learn about NDays - the comprehensive platform for discovering national day celebrations, cultural festivals, and independence days from around the world.',
};

export default function AboutPage() {
  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-6">
            About NDays
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            We're on a mission to connect the world through the celebration of national days, 
            cultural traditions, and the rich heritage that makes each country unique.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Our Mission</h2>
          <div className="bg-muted/50 rounded-lg p-8">
            <p className="text-lg leading-relaxed text-center">
              NDays exists to bridge cultures and foster global understanding by providing authentic, 
              verified content about national celebrations from every corner of the world. We believe 
              that by learning about and celebrating each other's traditions, we can build a more 
              connected and empathetic global community.
            </p>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Authenticity</CardTitle>
                <CardDescription>
                  Every piece of content is verified and sourced from official channels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  We work exclusively with government sources, cultural institutions, and verified 
                  content creators to ensure the accuracy and authenticity of all celebrations featured 
                  on our platform.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Global Inclusivity</CardTitle>
                <CardDescription>
                  Representing every nation and territory without bias or preference
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  From the smallest island nations to the largest countries, every culture deserves 
                  representation. We strive to provide equal coverage and respect for all national 
                  celebrations worldwide.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Community Driven</CardTitle>
                <CardDescription>
                  Built with input from cultural experts and community members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Our content is enriched by contributions from cultural ambassadors, historians, 
                  and community members who help us tell the complete story of each celebration.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Heart className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Cultural Respect</CardTitle>
                <CardDescription>
                  Honoring traditions with sensitivity and cultural awareness
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  We approach every culture with deep respect and sensitivity, ensuring that 
                  celebrations are presented in their proper context and with appropriate reverence.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Story Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Our Story</h2>
          <div className="prose prose-lg max-w-none">
            <p className="text-muted-foreground leading-relaxed mb-6">
              NDays was born from a simple observation: in our increasingly connected world, 
              we often know more about global brands than we do about the rich cultural 
              traditions of our neighbors. While social media connects us instantly, it rarely 
              helps us understand the deeper meaning behind the celebrations we see.
            </p>
            <p className="text-muted-foreground leading-relaxed mb-6">
              Our founders, a diverse team of cultural enthusiasts and technology experts, 
              recognized the need for a dedicated platform that could serve as a bridge between 
              cultures. They envisioned a space where anyone could discover the stories behind 
              national celebrations, understand their historical significance, and appreciate 
              the diversity of human expression.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              Today, NDays serves thousands of users worldwide, from students researching 
              cultural projects to travelers planning their journeys around local celebrations, 
              to families wanting to teach their children about global diversity. We're proud 
              to be part of fostering greater cultural understanding and appreciation.
            </p>
          </div>
        </div>

        {/* Impact Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Our Impact</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary mb-2">195+</div>
              <div className="text-sm text-muted-foreground">Countries Represented</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary mb-2">50K+</div>
              <div className="text-sm text-muted-foreground">Monthly Visitors</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary mb-2">1,200+</div>
              <div className="text-sm text-muted-foreground">Celebrations Documented</div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center bg-muted/50 rounded-lg p-8">
          <h2 className="text-2xl font-bold mb-4">Get in Touch</h2>
          <p className="text-muted-foreground mb-6">
            Have a celebration we should feature? Want to contribute content? 
            We'd love to hear from you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="mailto:<EMAIL>" 
              className="text-primary hover:underline font-medium"
            >
              <EMAIL>
            </a>
            <span className="hidden sm:inline text-muted-foreground">•</span>
            <a 
              href="mailto:<EMAIL>" 
              className="text-primary hover:underline font-medium"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
