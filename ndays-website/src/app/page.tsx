import Link from 'next/link';
import { Calendar, Globe, Search, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LocationDetector } from '@/components/LocationDetector';

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20 lg:py-32">
        <div className="container">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Discover National Days
              <span className="text-primary"> Around the World</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Explore authentic celebrations, cultural traditions, and national holidays from every country.
              Connect with the world through verified content and official celebrations.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/celebrations">
                  <Search className="mr-2 h-4 w-4" />
                  Explore Celebrations
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/countries">
                  <Globe className="mr-2 h-4 w-4" />
                  Browse Countries
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Location Detection Section */}
      <section className="py-12 bg-muted/30">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <LocationDetector />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 lg:py-32">
        <div className="container">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Everything you need to celebrate
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              From independence days to cultural festivals, discover the stories behind every celebration.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <Calendar className="h-8 w-8 text-primary" />
                  <CardTitle>Authentic Content</CardTitle>
                  <CardDescription>
                    Curated content from official sources and verified channels
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Every celebration is backed by authentic videos, images, and information
                    from government sources and major cultural institutions.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Globe className="h-8 w-8 text-primary" />
                  <CardTitle>Global Coverage</CardTitle>
                  <CardDescription>
                    Celebrations from every country and territory
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Comprehensive database covering national days, independence celebrations,
                    and cultural festivals from all 195 countries.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Search className="h-8 w-8 text-primary" />
                  <CardTitle>Smart Discovery</CardTitle>
                  <CardDescription>
                    Find celebrations by country, date, or category
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Advanced search and filtering options help you discover celebrations
                    that matter to you, with personalized recommendations.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-muted/50 py-20 lg:py-32">
        <div className="container">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Trusted by celebration enthusiasts worldwide
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-3">
              <div className="text-center">
                <div className="text-4xl font-bold text-primary">195+</div>
                <div className="mt-2 text-sm text-muted-foreground">Countries Covered</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary">1000+</div>
                <div className="mt-2 text-sm text-muted-foreground">National Celebrations</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary">5000+</div>
                <div className="mt-2 text-sm text-muted-foreground">Authentic Videos</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 lg:py-32">
        <div className="container">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Start exploring today
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Join thousands of users discovering the rich cultural heritage of our world.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/celebrations">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Get Started
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
