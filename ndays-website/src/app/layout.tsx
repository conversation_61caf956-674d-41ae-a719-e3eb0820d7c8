import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "NDays - National Day Celebrations Around the World",
    template: "%s | NDays"
  },
  description: "Discover and celebrate national days from around the world. Explore authentic content, videos, and cultural traditions from every country.",
  keywords: ["national days", "celebrations", "culture", "traditions", "holidays", "countries", "independence day"],
  authors: [{ name: "NDays Team" }],
  creator: "NDays",
  publisher: "NDays",
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title: "NDays - National Day Celebrations Around the World",
    description: "Discover and celebrate national days from around the world. Explore authentic content, videos, and cultural traditions from every country.",
    siteName: "NDays",
  },
  twitter: {
    card: "summary_large_image",
    title: "NDays - National Day Celebrations Around the World",
    description: "Discover and celebrate national days from around the world. Explore authentic content, videos, and cultural traditions from every country.",
    creator: "@ndays",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
