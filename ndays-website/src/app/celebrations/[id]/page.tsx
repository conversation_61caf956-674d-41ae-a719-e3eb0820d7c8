import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Calendar, MapPin, Tag, ExternalLink, Share2, ArrowLeft, Play } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/utils/date';

// Sample data - this would come from the database in a real implementation
const sampleCelebrations = {
  '1': {
    id: '1',
    title: 'Independence Day',
    description: 'Celebrating the independence of the United States with fireworks, parades, and patriotic displays. This historic day marks the adoption of the Declaration of Independence on July 4, 1776, when the thirteen American colonies declared themselves independent from British rule.',
    date: '2024-07-04',
    country: { name: 'United States', iso_code: 'USA' },
    category: 'independence',
    tags: ['independence', 'patriotic', 'fireworks', 'freedom', 'history'],
    mediaContent: [
      {
        id: '1',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Fourth of July Fireworks Display',
        description: 'Spectacular fireworks display over Washington D.C.',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      },
      {
        id: '2',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Independence Day Parade',
        description: 'Annual Independence Day parade in New York City',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  },
  '2': {
    id: '2',
    title: 'Bastille Day',
    description: 'French national day commemorating the storming of the Bastille fortress in 1789. This pivotal event marked the beginning of the French Revolution and the fight for liberty, equality, and fraternity.',
    date: '2024-07-14',
    country: { name: 'France', iso_code: 'FRA' },
    category: 'independence',
    tags: ['revolution', 'freedom', 'national', 'liberty', 'history'],
    mediaContent: [
      {
        id: '3',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Bastille Day Military Parade',
        description: 'Annual military parade on the Champs-Élysées',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  }
};

interface PageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params;
  const celebration = sampleCelebrations[id as keyof typeof sampleCelebrations];
  
  if (!celebration) {
    return {
      title: 'Celebration Not Found',
    };
  }

  return {
    title: `${celebration.title} - ${celebration.country.name}`,
    description: celebration.description,
    openGraph: {
      title: `${celebration.title} - ${celebration.country.name}`,
      description: celebration.description,
      type: 'article',
    },
  };
}

export default async function CelebrationDetailPage({ params }: PageProps) {
  const { id } = await params;
  const celebration = sampleCelebrations[id as keyof typeof sampleCelebrations];

  if (!celebration) {
    notFound();
  }

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/celebrations">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Celebrations
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            {celebration.title}
          </h1>
          <div className="flex flex-wrap items-center gap-4 text-muted-foreground mb-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span className="font-medium">{celebration.country.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>{formatDate(celebration.date)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span className="capitalize">{celebration.category}</span>
            </div>
          </div>
          
          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-6">
            {celebration.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary text-secondary-foreground"
              >
                {tag}
              </span>
            ))}
          </div>

          {/* Share Button */}
          <div className="flex gap-2">
            <Button variant="outline">
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/countries/${celebration.country.iso_code.toLowerCase()}`}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Country
              </Link>
            </Button>
          </div>
        </div>

        {/* Description */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle>About This Celebration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg leading-relaxed">{celebration.description}</p>
            </CardContent>
          </Card>
        </div>

        {/* Media Content */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Videos & Media</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {celebration.mediaContent.map((media) => (
              <Card key={media.id} className="overflow-hidden">
                <div className="relative aspect-video bg-muted">
                  <img
                    src={media.thumbnail_url}
                    alt={media.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button size="lg" className="rounded-full">
                      <Play className="mr-2 h-5 w-5" />
                      Play
                    </Button>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-lg">{media.title}</CardTitle>
                  <CardDescription>{media.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {/* Related Celebrations */}
        <div>
          <h2 className="text-2xl font-bold mb-6">More from {celebration.country.name}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Memorial Day</CardTitle>
                <CardDescription>Honoring those who died in military service</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">May 27, 2024</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/celebrations/3">View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Thanksgiving Day</CardTitle>
                <CardDescription>National day of giving thanks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">November 28, 2024</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/celebrations/4">View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
