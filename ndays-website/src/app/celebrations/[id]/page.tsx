import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Calendar, MapPin, Tag, ExternalLink, ArrowLeft, Play } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SocialShare } from '@/components/SocialShare';
import { formatDate } from '@/utils/date';
import { getCelebrationById } from '@/lib/notion';

// Server-side data fetching for celebration details
async function getCelebrationData(id: string) {
  try {
    const celebration = await getCelebrationById(id);
    return celebration;
  } catch (error) {
    console.error('Error fetching celebration data:', error);
    return null;
  }
}

interface PageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params;
  const celebration = await getCelebrationData(id);

  if (!celebration) {
    return {
      title: 'Celebration Not Found',
    };
  }

  return {
    title: `${celebration.title} - ${celebration.country?.name}`,
    description: celebration.description || `Discover ${celebration.title}, a national celebration from ${celebration.country?.name}.`,
    keywords: [
      celebration.title,
      celebration.country?.name || '',
      'national day',
      'celebration',
      'festival',
      ...(celebration.tags || [])
    ],
    openGraph: {
      title: `${celebration.title} - ${celebration.country?.name}`,
      description: celebration.description || `Discover ${celebration.title}, a national celebration from ${celebration.country?.name}.`,
      type: 'article',
      locale: 'en_US',
      siteName: 'NDays - National Day Celebrations',
      images: ['/og-celebration.jpg'],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${celebration.title} - ${celebration.country?.name}`,
      description: celebration.description || `Discover ${celebration.title}, a national celebration from ${celebration.country?.name}.`,
      images: ['/og-celebration.jpg'],
    },
  };
}

export default async function CelebrationDetailPage({ params }: PageProps) {
  const { id } = await params;
  const celebration = await getCelebrationData(id);

  if (!celebration) {
    notFound();
  }

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/celebrations">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Celebrations
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-4xl font-bold tracking-tight mb-4">{celebration.title}</h1>
              <div className="flex items-center space-x-6 text-muted-foreground mb-4">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span className="text-lg">{formatDate(celebration.date)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span className="text-lg">{celebration.country?.name}</span>
                </div>
                <span className="capitalize text-lg font-medium">{celebration.category}</span>
              </div>
            </div>
          </div>

          <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
            {celebration.description}
          </p>

          {/* Tags */}
          <div className="flex gap-2 flex-wrap mb-6">
            {celebration.tags?.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary text-secondary-foreground"
              >
                <Tag className="mr-1 h-3 w-3" />
                {tag}
              </span>
            ))}
          </div>

          {/* Share Button */}
          <div className="flex gap-2">
            <SocialShare
              url={`/celebrations/${id}`}
              title={celebration.title}
              description={celebration.description || ''}
              hashtags={['nationalday', celebration.country?.name.toLowerCase().replace(/\s+/g, '') || '', 'celebration']}
            />
          </div>
        </div>

        {/* Media Content */}
        {celebration.media_content && celebration.media_content.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-6">Media & Videos</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {celebration.media_content.map((media) => (
                <Card key={media.id} className="overflow-hidden">
                  <div className="aspect-video bg-muted relative">
                    {media.type === 'youtube' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Play className="h-12 w-12 text-white bg-red-600 rounded-full p-3" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-2">{media.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">{media.description}</p>
                    <Button variant="outline" size="sm" asChild>
                      <a href={media.url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Watch Video
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
