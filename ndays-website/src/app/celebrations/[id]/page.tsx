import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Calendar, MapPin, Tag, ExternalLink, ArrowLeft, Play } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SocialShare } from '@/components/SocialShare';
import { formatDate } from '@/utils/date';

// Sample data - this would come from the database in a real implementation
const sampleCelebrations = {
  '1': {
    id: '1',
    title: 'Independence Day',
    description: 'Celebrating the independence of the United States with fireworks, parades, and patriotic displays. This historic day marks the adoption of the Declaration of Independence on July 4, 1776, when the thirteen American colonies declared themselves independent from British rule.',
    date: '2024-07-04',
    country: { name: 'United States', iso_code: 'USA' },
    category: 'independence',
    tags: ['independence', 'patriotic', 'fireworks', 'freedom', 'history'],
    mediaContent: [
      {
        id: '1',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Fourth of July Fireworks Display',
        description: 'Spectacular fireworks display over Washington D.C.',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      },
      {
        id: '2',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Independence Day Parade',
        description: 'Annual Independence Day parade in New York City',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  },
  '2': {
    id: '2',
    title: 'Bastille Day',
    description: 'French national day commemorating the storming of the Bastille fortress in 1789. This pivotal event marked the beginning of the French Revolution and the fight for liberty, equality, and fraternity.',
    date: '2024-07-14',
    country: { name: 'France', iso_code: 'FRA' },
    category: 'independence',
    tags: ['revolution', 'freedom', 'national', 'liberty', 'history'],
    mediaContent: [
      {
        id: '3',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Bastille Day Military Parade',
        description: 'Annual military parade on the Champs-Élysées',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  },
  '7': {
    id: '7',
    title: 'Saudi National Day',
    description: 'Commemorating the unification of the Kingdom of Saudi Arabia by King Abdulaziz Al Saud in 1932. This historic day celebrates the establishment of the modern Saudi state and is marked with spectacular fireworks, cultural performances, traditional dances, and patriotic displays across all regions of the kingdom. Citizens wear traditional attire and the national colors of green and white.',
    date: '2024-09-23',
    country: { name: 'Saudi Arabia', iso_code: 'SAU' },
    category: 'independence',
    tags: ['national', 'unification', 'heritage', 'patriotic', 'kingdom'],
    mediaContent: [
      {
        id: '7',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Saudi National Day Fireworks in Riyadh',
        description: 'Spectacular fireworks display celebrating Saudi National Day in the capital',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      },
      {
        id: '8',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Traditional Saudi Ardah Dance Performance',
        description: 'Traditional sword dance performed during National Day celebrations',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  },
  '8': {
    id: '8',
    title: 'Saudi Founding Day',
    description: 'Celebrating the founding of the first Saudi state in 1727 by Imam Muhammad bin Saud in Diriyah. This day honors the rich heritage, culture, and historical journey of Saudi Arabia. Citizens celebrate by wearing traditional Saudi attire, participating in cultural events, and learning about the kingdom\'s founding history.',
    date: '2024-02-22',
    country: { name: 'Saudi Arabia', iso_code: 'SAU' },
    category: 'cultural',
    tags: ['founding', 'heritage', 'culture', 'history', 'diriyah'],
    mediaContent: [
      {
        id: '9',
        type: 'youtube',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        title: 'Saudi Founding Day Cultural Festival',
        description: 'Cultural performances and traditional crafts during Founding Day',
        thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
      }
    ]
  }
};

interface PageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params;
  const celebration = sampleCelebrations[id as keyof typeof sampleCelebrations];

  if (!celebration) {
    return {
      title: 'Celebration Not Found',
    };
  }

  return {
    title: `${celebration.title} - ${celebration.country.name}`,
    description: celebration.description,
    keywords: celebration.tags.join(', '),
    openGraph: {
      title: `${celebration.title} - ${celebration.country.name}`,
      description: celebration.description,
      type: 'article',
      siteName: 'NDays - National Day Celebrations',
      locale: 'en_US',
      images: [
        {
          url: '/og-celebration.jpg',
          width: 1200,
          height: 630,
          alt: `${celebration.title} celebration in ${celebration.country.name}`,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${celebration.title} - ${celebration.country.name}`,
      description: celebration.description,
      images: ['/og-celebration.jpg'],
    },
  };
}

export default async function CelebrationDetailPage({ params }: PageProps) {
  const { id } = await params;
  const celebration = sampleCelebrations[id as keyof typeof sampleCelebrations];

  if (!celebration) {
    notFound();
  }

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/celebrations">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Celebrations
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            {celebration.title}
          </h1>
          <div className="flex flex-wrap items-center gap-4 text-muted-foreground mb-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span className="font-medium">{celebration.country.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>{formatDate(celebration.date)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span className="capitalize">{celebration.category}</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-6">
            {celebration.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary text-secondary-foreground"
              >
                {tag}
              </span>
            ))}
          </div>

          {/* Share and Actions */}
          <div className="flex gap-2">
            <SocialShare
              url={`/celebrations/${celebration.id}`}
              title={`${celebration.title} - ${celebration.country.name}`}
              description={celebration.description}
              hashtags={celebration.tags}
            />
            <Button variant="outline" asChild>
              <Link href={`/countries/${celebration.country.iso_code.toLowerCase()}`}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Country
              </Link>
            </Button>
          </div>
        </div>

        {/* Description */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle>About This Celebration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg leading-relaxed">{celebration.description}</p>
            </CardContent>
          </Card>
        </div>

        {/* Media Content */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6">Videos & Media</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {celebration.mediaContent.map((media) => (
              <Card key={media.id} className="overflow-hidden">
                <div className="relative aspect-video bg-muted">
                  <img
                    src={media.thumbnail_url}
                    alt={media.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button size="lg" className="rounded-full">
                      <Play className="mr-2 h-5 w-5" />
                      Play
                    </Button>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-lg">{media.title}</CardTitle>
                  <CardDescription>{media.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {/* Related Celebrations */}
        <div>
          <h2 className="text-2xl font-bold mb-6">More from {celebration.country.name}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Memorial Day</CardTitle>
                <CardDescription>Honoring those who died in military service</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">May 27, 2024</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/celebrations/3">View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Thanksgiving Day</CardTitle>
                <CardDescription>National day of giving thanks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">November 28, 2024</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/celebrations/4">View Details</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
