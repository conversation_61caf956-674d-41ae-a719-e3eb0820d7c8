'use client';

import { useState, useMemo } from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Calendar, MapPin, Tag, ExternalLink, Search, X, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { formatDate } from '@/utils/date';

// Note: metadata export removed because this is a client component
// Metadata should be handled by a parent server component or layout

// Sample data - this would come from the database in a real implementation
const allCelebrations = [
  {
    id: '1',
    title: 'Independence Day',
    description: 'Celebrating the independence of the United States with fireworks, parades, and patriotic displays.',
    date: '2024-07-04',
    country: { name: 'United States', iso_code: 'USA' },
    category: 'independence',
    tags: ['independence', 'patriotic', 'fireworks'],
    mediaCount: 15
  },
  {
    id: '2',
    title: 'Bastille Day',
    description: 'French national day commemorating the storming of the Bastille fortress in 1789.',
    date: '2024-07-14',
    country: { name: 'France', iso_code: 'FRA' },
    category: 'independence',
    tags: ['revolution', 'freedom', 'national'],
    mediaCount: 12
  },
  {
    id: '3',
    title: 'Canada Day',
    description: 'Celebrating the anniversary of Canadian Confederation with festivals and fireworks.',
    date: '2024-07-01',
    country: { name: 'Canada', iso_code: 'CAN' },
    category: 'independence',
    tags: ['confederation', 'national', 'celebration'],
    mediaCount: 8
  },
  {
    id: '4',
    title: 'Australia Day',
    description: 'National day of Australia commemorating the arrival of the First Fleet.',
    date: '2024-01-26',
    country: { name: 'Australia', iso_code: 'AUS' },
    category: 'independence',
    tags: ['national', 'heritage', 'celebration'],
    mediaCount: 10
  },
  {
    id: '5',
    title: 'German Unity Day',
    description: 'Commemorating the reunification of Germany in 1990.',
    date: '2024-10-03',
    country: { name: 'Germany', iso_code: 'DEU' },
    category: 'independence',
    tags: ['unity', 'reunification', 'national'],
    mediaCount: 7
  },
  {
    id: '6',
    title: 'Constitution Day',
    description: 'Celebrating the Norwegian Constitution signed in 1814.',
    date: '2024-05-17',
    country: { name: 'Norway', iso_code: 'NOR' },
    category: 'independence',
    tags: ['constitution', 'national', 'democracy'],
    mediaCount: 6
  },
  {
    id: '7',
    title: 'Saudi National Day',
    description: 'Commemorating the unification of the Kingdom of Saudi Arabia by King Abdulaziz Al Saud in 1932.',
    date: '2024-09-23',
    country: { name: 'Saudi Arabia', iso_code: 'SAU' },
    category: 'independence',
    tags: ['national', 'unification', 'heritage', 'patriotic'],
    mediaCount: 18
  },
  {
    id: '8',
    title: 'Saudi Founding Day',
    description: 'Celebrating the founding of the first Saudi state in 1727 by Imam Muhammad bin Saud.',
    date: '2024-02-22',
    country: { name: 'Saudi Arabia', iso_code: 'SAU' },
    category: 'cultural',
    tags: ['founding', 'heritage', 'culture', 'history'],
    mediaCount: 12
  },
  {
    id: '9',
    title: 'Eid al-Fitr',
    description: 'The festival marking the end of Ramadan, celebrated with special prayers and family gatherings.',
    date: '2024-04-10',
    country: { name: 'Saudi Arabia', iso_code: 'SAU' },
    category: 'religious',
    tags: ['eid', 'ramadan', 'islamic', 'family', 'celebration'],
    mediaCount: 25
  }
];

const ITEMS_PER_PAGE = 6;

export default function CelebrationsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and search celebrations
  const filteredCelebrations = useMemo(() => {
    let filtered = allCelebrations;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = allCelebrations.filter(celebration =>
        celebration.title.toLowerCase().includes(query) ||
        celebration.description.toLowerCase().includes(query) ||
        celebration.country.name.toLowerCase().includes(query) ||
        celebration.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(celebration => celebration.category === selectedCategory);
    }

    return filtered;
  }, [searchQuery, selectedCategory]);

  const displayedCelebrations = filteredCelebrations.slice(0, currentPage * ITEMS_PER_PAGE);
  const hasMoreCelebrations = displayedCelebrations.length < filteredCelebrations.length;

  const loadMoreCelebrations = async () => {
    if (isLoading || !hasMoreCelebrations) return;

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 800));
    setCurrentPage(prev => prev + 1);
    setIsLoading(false);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            National Day Celebrations
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover independence days, cultural festivals, and historical commemorations
            from every corner of the world.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search celebrations, countries, or tags..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Category Filters and Results Count */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('all')}
              >
                All Categories
              </Button>
              <Button
                variant={selectedCategory === 'independence' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('independence')}
              >
                Independence
              </Button>
              <Button
                variant={selectedCategory === 'cultural' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('cultural')}
              >
                Cultural
              </Button>
              <Button
                variant={selectedCategory === 'religious' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('religious')}
              >
                Religious
              </Button>
              <Button
                variant={selectedCategory === 'historical' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryChange('historical')}
              >
                Historical
              </Button>
            </div>
            <div className="text-sm text-muted-foreground">
              {filteredCelebrations.length} celebrations found
              {searchQuery && (
                <span className="ml-2 text-primary">
                  (filtered by "{searchQuery}")
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Celebrations Grid */}
        {filteredCelebrations.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No celebrations found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search terms or category filters to find celebrations.
            </p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={clearSearch}>
                Clear Search
              </Button>
              <Button variant="outline" onClick={() => handleCategoryChange('all')}>
                Show All Categories
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {displayedCelebrations.map((celebration) => (
              <Card key={celebration.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{celebration.title}</CardTitle>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{celebration.country.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(celebration.date)}</span>
                        </div>
                      </div>
                      <CardDescription className="line-clamp-2">
                        {celebration.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Tags */}
                    <div className="flex items-center space-x-2">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <div className="flex gap-1 flex-wrap">
                        {celebration.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary text-secondary-foreground"
                          >
                            {tag}
                          </span>
                        ))}
                        {celebration.tags.length > 3 && (
                          <span className="text-xs text-muted-foreground">
                            +{celebration.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        {celebration.mediaCount} media items
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/celebrations/${celebration.id}`}>
                            <ExternalLink className="h-4 w-4 mr-1" />
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More */}
        {hasMoreCelebrations && filteredCelebrations.length > 0 && (
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              onClick={loadMoreCelebrations}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                'Load More Celebrations'
              )}
            </Button>
          </div>
        )}

        {!hasMoreCelebrations && displayedCelebrations.length > ITEMS_PER_PAGE && (
          <div className="text-center mt-12">
            <p className="text-muted-foreground">
              You've viewed all {filteredCelebrations.length} celebrations
            </p>
          </div>
        )}

        {/* Featured Categories */}
        <div className="mt-20">
          <h2 className="text-2xl font-bold text-center mb-8">Browse by Category</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { name: 'Independence Days', count: 195, color: 'bg-red-500' },
              { name: 'Cultural Festivals', count: 450, color: 'bg-blue-500' },
              { name: 'Religious Holidays', count: 320, color: 'bg-green-500' },
              { name: 'Historical Events', count: 235, color: 'bg-purple-500' }
            ].map((category) => (
              <Card key={category.name} className="text-center hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className={`w-12 h-12 ${category.color} rounded-lg mx-auto mb-3 flex items-center justify-center`}>
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold mb-1">{category.name}</h3>
                  <p className="text-sm text-muted-foreground">{category.count} celebrations</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
