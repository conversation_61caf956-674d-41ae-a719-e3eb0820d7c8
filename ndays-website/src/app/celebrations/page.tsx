import { Metadata } from 'next';
import Link from 'next/link';
import { Calendar, MapPin, Tag, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/utils/date';

export const metadata: Metadata = {
  title: 'All Celebrations',
  description: 'Browse all national day celebrations, independence days, and cultural festivals from around the world. Filter by country, date, or category.',
};

// Sample data - this would come from the database in a real implementation
const sampleCelebrations = [
  {
    id: '1',
    title: 'Independence Day',
    description: 'Celebrating the independence of the United States with fireworks, parades, and patriotic displays.',
    date: '2024-07-04',
    country: { name: 'United States', iso_code: 'USA' },
    category: 'independence',
    tags: ['independence', 'patriotic', 'fireworks'],
    mediaCount: 15
  },
  {
    id: '2',
    title: 'Bastille Day',
    description: 'French national day commemorating the storming of the Bastille fortress in 1789.',
    date: '2024-07-14',
    country: { name: 'France', iso_code: 'FRA' },
    category: 'independence',
    tags: ['revolution', 'freedom', 'national'],
    mediaCount: 12
  },
  {
    id: '3',
    title: 'Canada Day',
    description: 'Celebrating the anniversary of Canadian Confederation with festivals and fireworks.',
    date: '2024-07-01',
    country: { name: 'Canada', iso_code: 'CAN' },
    category: 'independence',
    tags: ['confederation', 'national', 'celebration'],
    mediaCount: 8
  },
  {
    id: '4',
    title: 'Australia Day',
    description: 'National day of Australia commemorating the arrival of the First Fleet.',
    date: '2024-01-26',
    country: { name: 'Australia', iso_code: 'AUS' },
    category: 'independence',
    tags: ['national', 'heritage', 'celebration'],
    mediaCount: 10
  },
  {
    id: '5',
    title: 'German Unity Day',
    description: 'Commemorating the reunification of Germany in 1990.',
    date: '2024-10-03',
    country: { name: 'Germany', iso_code: 'DEU' },
    category: 'independence',
    tags: ['unity', 'reunification', 'national'],
    mediaCount: 7
  },
  {
    id: '6',
    title: 'Constitution Day',
    description: 'Celebrating the Norwegian Constitution signed in 1814.',
    date: '2024-05-17',
    country: { name: 'Norway', iso_code: 'NOR' },
    category: 'independence',
    tags: ['constitution', 'national', 'democracy'],
    mediaCount: 6
  }
];

export default function CelebrationsPage() {
  return (
    <div className="container py-8">
      <div className="mx-auto max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            National Day Celebrations
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover independence days, cultural festivals, and historical commemorations 
            from every corner of the world.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              <Button variant="outline" size="sm">All Categories</Button>
              <Button variant="outline" size="sm">Independence</Button>
              <Button variant="outline" size="sm">Cultural</Button>
              <Button variant="outline" size="sm">Religious</Button>
              <Button variant="outline" size="sm">Historical</Button>
            </div>
            <div className="text-sm text-muted-foreground">
              {sampleCelebrations.length} celebrations found
            </div>
          </div>
        </div>

        {/* Celebrations Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sampleCelebrations.map((celebration) => (
            <Card key={celebration.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-xl mb-2">{celebration.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{celebration.country.name}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(celebration.date)}</span>
                      </div>
                    </div>
                    <CardDescription className="line-clamp-2">
                      {celebration.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Tags */}
                  <div className="flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <div className="flex gap-1 flex-wrap">
                      {celebration.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary text-secondary-foreground"
                        >
                          {tag}
                        </span>
                      ))}
                      {celebration.tags.length > 3 && (
                        <span className="text-xs text-muted-foreground">
                          +{celebration.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      {celebration.mediaCount} media items
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/celebrations/${celebration.id}`}>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Celebrations
          </Button>
        </div>

        {/* Featured Categories */}
        <div className="mt-20">
          <h2 className="text-2xl font-bold text-center mb-8">Browse by Category</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { name: 'Independence Days', count: 195, color: 'bg-red-500' },
              { name: 'Cultural Festivals', count: 450, color: 'bg-blue-500' },
              { name: 'Religious Holidays', count: 320, color: 'bg-green-500' },
              { name: 'Historical Events', count: 235, color: 'bg-purple-500' }
            ].map((category) => (
              <Card key={category.name} className="text-center hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className={`w-12 h-12 ${category.color} rounded-lg mx-auto mb-3 flex items-center justify-center`}>
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold mb-1">{category.name}</h3>
                  <p className="text-sm text-muted-foreground">{category.count} celebrations</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
