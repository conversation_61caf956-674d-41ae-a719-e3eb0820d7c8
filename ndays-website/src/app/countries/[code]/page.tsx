import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Calendar, Globe, MapPin, ArrowLeft, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/utils/date';

// Sample data - this would come from the database in a real implementation
const sampleCountries = {
  'usa': {
    id: '1',
    name: 'United States',
    iso_code: 'USA',
    timezone: 'America/New_York',
    description: 'The United States of America is a federal republic composed of 50 states, a federal district, five major self-governing territories, and various possessions.',
    celebrations: [
      {
        id: '1',
        title: 'Independence Day',
        description: 'Celebrating the independence of the United States with fireworks, parades, and patriotic displays.',
        date: '2024-07-04',
        category: 'independence',
        tags: ['independence', 'patriotic', 'fireworks']
      },
      {
        id: '5',
        title: 'Memorial Day',
        description: 'Honoring those who died in military service to the United States.',
        date: '2024-05-27',
        category: 'remembrance',
        tags: ['memorial', 'military', 'honor']
      },
      {
        id: '6',
        title: 'Thanksgiving Day',
        description: 'National day of giving thanks for the harvest and blessings of the past year.',
        date: '2024-11-28',
        category: 'cultural',
        tags: ['thanksgiving', 'harvest', 'family']
      }
    ]
  },
  'fra': {
    id: '2',
    name: 'France',
    iso_code: 'FRA',
    timezone: 'Europe/Paris',
    description: 'France is a country located in Western Europe, known for its rich history, culture, and contributions to art, philosophy, and cuisine.',
    celebrations: [
      {
        id: '2',
        title: 'Bastille Day',
        description: 'French national day commemorating the storming of the Bastille fortress in 1789.',
        date: '2024-07-14',
        category: 'independence',
        tags: ['revolution', 'freedom', 'national']
      }
    ]
  },
  'can': {
    id: '3',
    name: 'Canada',
    iso_code: 'CAN',
    timezone: 'America/Toronto',
    description: 'Canada is a country in North America consisting of ten provinces and three territories, known for its natural beauty and multicultural society.',
    celebrations: [
      {
        id: '3',
        title: 'Canada Day',
        description: 'Celebrating the anniversary of Canadian Confederation with festivals and fireworks.',
        date: '2024-07-01',
        category: 'independence',
        tags: ['confederation', 'national', 'celebration']
      }
    ]
  }
};

interface PageProps {
  params: Promise<{ code: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { code } = await params;
  const country = sampleCountries[code.toLowerCase() as keyof typeof sampleCountries];
  
  if (!country) {
    return {
      title: 'Country Not Found',
    };
  }

  return {
    title: `${country.name} - National Celebrations`,
    description: `Discover national day celebrations and cultural festivals from ${country.name}. ${country.description}`,
    openGraph: {
      title: `${country.name} - National Celebrations`,
      description: `Discover national day celebrations and cultural festivals from ${country.name}.`,
      type: 'website',
    },
  };
}

export default async function CountryDetailPage({ params }: PageProps) {
  const { code } = await params;
  const country = sampleCountries[code.toLowerCase() as keyof typeof sampleCountries];

  if (!country) {
    notFound();
  }

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-6xl">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/countries">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Countries
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
              <Globe className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight">{country.name}</h1>
              <div className="flex items-center space-x-4 text-muted-foreground mt-2">
                <span className="font-mono text-sm">{country.iso_code}</span>
                <span className="text-sm">{country.timezone}</span>
              </div>
            </div>
          </div>
          
          <p className="text-lg text-muted-foreground max-w-3xl">
            {country.description}
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations.length}
              </div>
              <div className="text-sm text-muted-foreground">National Celebrations</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations.filter(c => c.category === 'independence').length}
              </div>
              <div className="text-sm text-muted-foreground">Independence Days</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations.filter(c => c.category === 'cultural').length}
              </div>
              <div className="text-sm text-muted-foreground">Cultural Festivals</div>
            </CardContent>
          </Card>
        </div>

        {/* Celebrations */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">National Celebrations</h2>
            <Button variant="outline" size="sm">
              <Calendar className="mr-2 h-4 w-4" />
              View Calendar
            </Button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {country.celebrations.map((celebration) => (
              <Card key={celebration.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{celebration.title}</CardTitle>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(celebration.date)}</span>
                        </div>
                        <span className="capitalize">{celebration.category}</span>
                      </div>
                      <CardDescription className="line-clamp-2">
                        {celebration.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Tags */}
                    <div className="flex gap-1 flex-wrap">
                      {celebration.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary text-secondary-foreground"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/celebrations/${celebration.id}`}>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Related Countries */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Explore Other Countries</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(sampleCountries)
              .filter(([_, c]) => c.iso_code !== country.iso_code)
              .slice(0, 3)
              .map(([code, c]) => (
                <Card key={c.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-sm flex items-center justify-center">
                        <Globe className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{c.name}</h3>
                        <p className="text-xs text-muted-foreground">{c.iso_code}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {c.celebrations.length} celebrations
                      </span>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/countries/${code}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
