import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Calendar, Globe, ArrowLeft, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
import { SocialShare } from '@/components/SocialShare';
import { formatDate } from '@/utils/date';
import { getCountryByCode, getCountries, getCelebrationsByCountryCode } from '@/lib/notion';

// Server-side data fetching for country details
async function getCountryData(code: string) {
  try {
    const [country, celebrations] = await Promise.all([
      getCountryByCode(code),
      getCelebrationsByCountryCode(code)
    ]);

    if (!country) return null;

    return {
      ...country,
      celebrations
    };
  } catch (error) {
    console.error('Error fetching country data:', error);
    return null;
  }
}

async function getOtherCountries(excludeCode: string) {
  try {
    const countries = await getCountries();
    return countries.filter(c => c.iso_code !== excludeCode.toUpperCase()).slice(0, 3);
  } catch (error) {
    console.error('Error fetching other countries:', error);
    return [];
  }
}

interface PageProps {
  params: Promise<{ code: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { code } = await params;
  const country = await getCountryData(code);

  if (!country) {
    return {
      title: 'Country Not Found',
    };
  }

  return {
    title: `${country.name} - National Celebrations`,
    description: `Discover national day celebrations and cultural festivals from ${country.name}. Explore independence days, cultural events, and historical commemorations.`,
    keywords: [
      country.name,
      'national day',
      'celebrations',
      'independence day',
      'cultural festivals',
      'holidays',
      country.iso_code
    ],
    openGraph: {
      title: `${country.name} - National Celebrations`,
      description: `Discover national day celebrations and cultural festivals from ${country.name}.`,
      type: 'website',
      locale: 'en_US',
      siteName: 'NDays - National Day Celebrations',
      images: ['/og-country.jpg'],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${country.name} - National Celebrations`,
      description: `Discover national day celebrations and cultural festivals from ${country.name}.`,
      images: ['/og-country.jpg'],
    },
  };
}

export default async function CountryDetailPage({ params }: PageProps) {
  const { code } = await params;
  const country = await getCountryData(code);

  if (!country) {
    notFound();
  }

  // const otherCountries = await getOtherCountries(code);

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-6xl">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="outline" asChild>
            <Link href="/countries">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Countries
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
              <Globe className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight">{country.name}</h1>
              <div className="flex items-center space-x-4 text-muted-foreground mt-2">
                <span className="font-mono text-sm">{country.iso_code}</span>
                <span className="text-sm">{country.timezone}</span>
              </div>
            </div>
          </div>

          <p className="text-lg text-muted-foreground max-w-3xl mb-6">
            {country.description}
          </p>

          {/* Share Button */}
          <div className="flex gap-2">
            <SocialShare
              url={`/countries/${code}`}
              title={`${country.name} - National Celebrations`}
              description={country.description}
              hashtags={['nationalday', country.name.toLowerCase().replace(/\s+/g, ''), 'celebrations']}
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations?.length || 0}
              </div>
              <div className="text-sm text-muted-foreground">National Celebrations</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations?.filter(c => c.category === 'independence').length || 0}
              </div>
              <div className="text-sm text-muted-foreground">Independence Days</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {country.celebrations?.filter(c => c.category === 'cultural').length || 0}
              </div>
              <div className="text-sm text-muted-foreground">Cultural Festivals</div>
            </CardContent>
          </Card>
        </div>

        {/* Celebrations */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">National Celebrations</h2>
            <Button variant="outline" size="sm">
              <Calendar className="mr-2 h-4 w-4" />
              View Calendar
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {country.celebrations?.map((celebration) => (
              <Card key={celebration.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{celebration.title}</CardTitle>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(celebration.date)}</span>
                        </div>
                        <span className="capitalize">{celebration.category}</span>
                      </div>
                      <CardDescription className="line-clamp-2">
                        {celebration.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Tags */}
                    <div className="flex gap-1 flex-wrap">
                      {celebration.tags?.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary text-secondary-foreground"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/celebrations/${celebration.id}`}>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Related Countries */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Explore Other Countries</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(sampleCountries)
              .filter(([_, c]) => c.iso_code !== country.iso_code)
              .slice(0, 3)
              .map(([code, c]) => (
                <Card key={c.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-sm flex items-center justify-center">
                        <Globe className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{c.name}</h3>
                        <p className="text-xs text-muted-foreground">{c.iso_code}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {c.celebrations.length} celebrations
                      </span>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/countries/${code}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
