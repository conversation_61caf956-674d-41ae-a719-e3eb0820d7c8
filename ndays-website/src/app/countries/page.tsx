'use client';

import { useState, useMemo } from 'react';
import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Globe, Calendar, Loader2, Search, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Note: metadata export removed because this is a client component
// Metadata should be handled by a parent server component or layout

// Sample data - this would come from the database in a real implementation
const allCountries = [
  { id: '1', name: 'United States', iso_code: 'USA', celebrationCount: 12 },
  { id: '2', name: 'United Kingdom', iso_code: 'GBR', celebrationCount: 8 },
  { id: '3', name: 'France', iso_code: 'FRA', celebrationCount: 15 },
  { id: '4', name: 'Germany', iso_code: 'DEU', celebrationCount: 10 },
  { id: '5', name: 'Japan', iso_code: 'JPN', celebrationCount: 18 },
  { id: '6', name: 'Australia', iso_code: 'AUS', celebrationCount: 7 },
  { id: '7', name: 'Canada', iso_code: 'CAN', celebrationCount: 9 },
  { id: '8', name: 'India', iso_code: 'IND', celebrationCount: 25 },
  { id: '9', name: 'Brazil', iso_code: 'BRA', celebrationCount: 14 },
  { id: '10', name: 'Mexico', iso_code: 'MEX', celebrationCount: 16 },
  { id: '11', name: 'Saudi Arabia', iso_code: 'SAU', celebrationCount: 4 },
  { id: '12', name: 'Italy', iso_code: 'ITA', celebrationCount: 11 },
  { id: '13', name: 'Spain', iso_code: 'ESP', celebrationCount: 13 },
  { id: '14', name: 'China', iso_code: 'CHN', celebrationCount: 20 },
  { id: '15', name: 'South Korea', iso_code: 'KOR', celebrationCount: 9 },
  { id: '16', name: 'Netherlands', iso_code: 'NLD', celebrationCount: 6 },
  { id: '17', name: 'Sweden', iso_code: 'SWE', celebrationCount: 5 },
  { id: '18', name: 'Norway', iso_code: 'NOR', celebrationCount: 4 },
  { id: '19', name: 'Denmark', iso_code: 'DNK', celebrationCount: 3 },
  { id: '20', name: 'Finland', iso_code: 'FIN', celebrationCount: 4 },
  { id: '21', name: 'Switzerland', iso_code: 'CHE', celebrationCount: 7 },
  { id: '22', name: 'United Arab Emirates', iso_code: 'ARE', celebrationCount: 5 },
  { id: '23', name: 'Egypt', iso_code: 'EGY', celebrationCount: 8 },
  { id: '24', name: 'Jordan', iso_code: 'JOR', celebrationCount: 6 },
  { id: '25', name: 'Lebanon', iso_code: 'LBN', celebrationCount: 7 },
  { id: '26', name: 'Kuwait', iso_code: 'KWT', celebrationCount: 4 },
  { id: '27', name: 'Qatar', iso_code: 'QAT', celebrationCount: 3 },
  { id: '28', name: 'Bahrain', iso_code: 'BHR', celebrationCount: 3 },
  { id: '29', name: 'Oman', iso_code: 'OMN', celebrationCount: 4 },
  { id: '30', name: 'Russia', iso_code: 'RUS', celebrationCount: 15 },
];

const ITEMS_PER_PAGE = 9;

export default function CountriesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'celebrations'>('name');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and sort countries based on search and sort criteria
  const filteredAndSortedCountries = useMemo(() => {
    let filtered = allCountries;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = allCountries.filter(country =>
        country.name.toLowerCase().includes(query) ||
        country.iso_code.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else {
        return b.celebrationCount - a.celebrationCount;
      }
    });

    return sorted;
  }, [searchQuery, sortBy]);

  const displayedCountries = filteredAndSortedCountries.slice(0, currentPage * ITEMS_PER_PAGE);
  const hasMoreCountries = displayedCountries.length < filteredAndSortedCountries.length;

  const loadMoreCountries = async () => {
    if (isLoading || !hasMoreCountries) return;

    setIsLoading(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 800));

    setCurrentPage(prev => prev + 1);
    setIsLoading(false);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handleSortChange = (newSortBy: 'name' | 'celebrations') => {
    setSortBy(newSortBy);
    setCurrentPage(1); // Reset to first page when sorting
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            Countries & Territories
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore national day celebrations from around the world. Each country has its unique
            traditions, independence days, and cultural festivals.
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search countries..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Stats and Sort */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {displayedCountries.length} of {filteredAndSortedCountries.length} countries
              {searchQuery && (
                <span className="ml-2 text-primary">
                  (filtered by "{searchQuery}")
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant={sortBy === 'name' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleSortChange('name')}
              >
                Sort by Name
              </Button>
              <Button
                variant={sortBy === 'celebrations' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleSortChange('celebrations')}
              >
                Sort by Celebrations
              </Button>
            </div>
          </div>
        </div>

        {/* Countries Grid */}
        {filteredAndSortedCountries.length === 0 ? (
          <div className="text-center py-12">
            <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No countries found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search terms or clear the search to see all countries.
            </p>
            <Button variant="outline" onClick={clearSearch}>
              Clear Search
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {displayedCountries.map((country) => (
              <Card key={country.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-sm flex items-center justify-center">
                      <Globe className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{country.name}</CardTitle>
                      <CardDescription>{country.iso_code}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{country.celebrationCount} celebrations</span>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/countries/${country.iso_code.toLowerCase()}`}>
                        View Details
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More */}
        {hasMoreCountries && (
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              onClick={loadMoreCountries}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                'Load More Countries'
              )}
            </Button>
          </div>
        )}

        {!hasMoreCountries && displayedCountries.length > ITEMS_PER_PAGE && (
          <div className="text-center mt-12">
            <p className="text-muted-foreground">
              You've viewed all {allCountries.length} countries
            </p>
          </div>
        )}

        {/* Stats Section */}
        <div className="mt-20 bg-muted/50 rounded-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold">Global Celebration Statistics</h2>
            <p className="text-muted-foreground mt-2">
              Discover the diversity of national celebrations worldwide
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">195+</div>
              <div className="text-sm text-muted-foreground">Countries & Territories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">1,200+</div>
              <div className="text-sm text-muted-foreground">National Celebrations</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">50+</div>
              <div className="text-sm text-muted-foreground">Celebration Categories</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
