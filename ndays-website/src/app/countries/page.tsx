import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { Globe, Calendar } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: 'Countries',
  description: 'Browse national day celebrations by country. Discover independence days, cultural festivals, and historical commemorations from around the world.',
};

// Sample data - this would come from the database in a real implementation
const sampleCountries = [
  { id: '1', name: 'United States', iso_code: 'USA', celebrationCount: 12 },
  { id: '2', name: 'United Kingdom', iso_code: 'GBR', celebrationCount: 8 },
  { id: '3', name: 'France', iso_code: 'FRA', celebrationCount: 15 },
  { id: '4', name: 'Germany', iso_code: 'DEU', celebrationCount: 10 },
  { id: '5', name: 'Japan', iso_code: 'JPN', celebrationCount: 18 },
  { id: '6', name: 'Australia', iso_code: 'AUS', celebrationCount: 7 },
  { id: '7', name: 'Canada', iso_code: 'CAN', celebrationCount: 9 },
  { id: '8', name: 'India', iso_code: 'IND', celebrationCount: 25 },
  { id: '9', name: 'Brazil', iso_code: 'BRA', celebrationCount: 14 },
  { id: '10', name: 'Mexico', iso_code: 'MEX', celebrationCount: 16 },
];

export default function CountriesPage() {
  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            Countries & Territories
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore national day celebrations from around the world. Each country has its unique 
            traditions, independence days, and cultural festivals.
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {sampleCountries.length} countries
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Sort by Name
              </Button>
              <Button variant="outline" size="sm">
                Sort by Celebrations
              </Button>
            </div>
          </div>
        </div>

        {/* Countries Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sampleCountries.map((country) => (
            <Card key={country.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-sm flex items-center justify-center">
                    <Globe className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{country.name}</CardTitle>
                    <CardDescription>{country.iso_code}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{country.celebrationCount} celebrations</span>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/countries/${country.iso_code.toLowerCase()}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Countries
          </Button>
        </div>

        {/* Stats Section */}
        <div className="mt-20 bg-muted/50 rounded-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold">Global Celebration Statistics</h2>
            <p className="text-muted-foreground mt-2">
              Discover the diversity of national celebrations worldwide
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">195+</div>
              <div className="text-sm text-muted-foreground">Countries & Territories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">1,200+</div>
              <div className="text-sm text-muted-foreground">National Celebrations</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">50+</div>
              <div className="text-sm text-muted-foreground">Celebration Categories</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
