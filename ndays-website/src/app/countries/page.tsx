'use client';

import { useState, useMemo } from 'react';
// import { Metadata } from 'next';
import Link from 'next/link';
import { Globe, Calendar, Loader2, Search, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCountriesWithCelebrationCount } from '@/lib/data-service';
import { LoadingState, CountryCardSkeleton } from '@/components/ui/loading';

// Note: metadata export removed because this is a client component
// Metadata should be handled by a parent server component or layout

const ITEMS_PER_PAGE = 9;

export default function CountriesPage() {
  const { data: allCountries, loading, error, refetch } = useCountriesWithCelebrationCount();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'celebrations'>('name');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and sort countries based on search and sort criteria
  const filteredAndSortedCountries = useMemo(() => {
    if (!allCountries) return [];

    let filtered = allCountries;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = allCountries.filter(country =>
        country.name.toLowerCase().includes(query) ||
        country.iso_code.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else {
        return (b.celebrationCount || 0) - (a.celebrationCount || 0);
      }
    });

    return sorted;
  }, [allCountries, searchQuery, sortBy]);

  const displayedCountries = filteredAndSortedCountries.slice(0, currentPage * ITEMS_PER_PAGE);
  const hasMoreCountries = displayedCountries.length < filteredAndSortedCountries.length;

  const loadMoreCountries = async () => {
    if (isLoading || !hasMoreCountries) return;

    setIsLoading(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 800));

    setCurrentPage(prev => prev + 1);
    setIsLoading(false);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handleSortChange = (newSortBy: 'name' | 'celebrations') => {
    setSortBy(newSortBy);
    setCurrentPage(1); // Reset to first page when sorting
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            Countries & Territories
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore national day celebrations from around the world. Each country has its unique
            traditions, independence days, and cultural festivals.
          </p>
        </div>

        <LoadingState
          loading={loading}
          error={error}
          loadingComponent={
            <div className="space-y-8">
              <div className="mb-8 space-y-4">
                <div className="h-10 bg-gray-200 rounded max-w-md animate-pulse"></div>
                <div className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                  <div className="h-8 bg-gray-200 rounded w-40 animate-pulse"></div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 9 }).map((_, i) => (
                  <CountryCardSkeleton key={i} />
                ))}
              </div>
            </div>
          }
          retryAction={refetch}
        >

          {/* Search and Filter Section */}
          <div className="mb-8 space-y-4">
            {/* Search Bar */}
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search countries..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 pr-10"
              />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Stats and Sort */}
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {displayedCountries.length} of {filteredAndSortedCountries.length} countries
                {searchQuery && (
                  <span className="ml-2 text-primary">
                    (filtered by "{searchQuery}")
                  </span>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  variant={sortBy === 'name' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleSortChange('name')}
                >
                  Sort by Name
                </Button>
                <Button
                  variant={sortBy === 'celebrations' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleSortChange('celebrations')}
                >
                  Sort by Celebrations
                </Button>
              </div>
            </div>
          </div>

          {/* Countries Grid */}
          {filteredAndSortedCountries.length === 0 ? (
            <div className="text-center py-12">
              <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No countries found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search terms or clear the search to see all countries.
              </p>
              <Button variant="outline" onClick={clearSearch}>
                Clear Search
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {displayedCountries.map((country) => (
                <Card key={country.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-sm flex items-center justify-center">
                        <Globe className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{country.name}</CardTitle>
                        <CardDescription>{country.iso_code}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>{country.celebrationCount || 0} celebrations</span>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/countries/${country.iso_code.toLowerCase()}`}>
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Load More */}
          {hasMoreCountries && (
            <div className="text-center mt-12">
              <Button
                variant="outline"
                size="lg"
                onClick={loadMoreCountries}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More Countries'
                )}
              </Button>
            </div>
          )}

          {!hasMoreCountries && displayedCountries.length > ITEMS_PER_PAGE && (
            <div className="text-center mt-12">
              <p className="text-muted-foreground">
                You've viewed all {allCountries?.length || 0} countries
              </p>
            </div>
          )}

          {/* Stats Section */}
          <div className="mt-20 bg-muted/50 rounded-lg p-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold">Global Celebration Statistics</h2>
              <p className="text-muted-foreground mt-2">
                Discover the diversity of national celebrations worldwide
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">195+</div>
                <div className="text-sm text-muted-foreground">Countries & Territories</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">1,200+</div>
                <div className="text-sm text-muted-foreground">National Celebrations</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">50+</div>
                <div className="text-sm text-muted-foreground">Celebration Categories</div>
              </div>
            </div>
          </div>
        </LoadingState>
      </div>
    </div>
  );
}
