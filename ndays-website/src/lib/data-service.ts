'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  getCountries,
  getCountriesWithCelebrationCount,
  getCountryByCode,
  getCelebrations,
  getCelebrationById,
  searchCelebrations,
  getCelebrationsByCountryCode,
  getUpcomingCelebrations,
  getCelebrationStats,
  CountryWithCelebrations,
  CelebrationWithDetails
} from './notion';
import { Country, Celebration } from '@/types';

// Loading state interface
export interface LoadingState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Hook for fetching countries with loading state
export function useCountries(): LoadingState<Country[]> {
  const [data, setData] = useState<Country[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const countries = await getCountries();
      setData(countries);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch countries');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching countries with celebration counts
export function useCountriesWithCelebrationCount(): LoadingState<CountryWithCelebrations[]> {
  const [data, setData] = useState<CountryWithCelebrations[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const countries = await getCountriesWithCelebrationCount();
      setData(countries);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch countries with celebration count');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching a single country by code
export function useCountryByCode(code: string): LoadingState<CountryWithCelebrations> {
  const [data, setData] = useState<CountryWithCelebrations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!code) return;

    try {
      setLoading(true);
      setError(null);
      const country = await getCountryByCode(code);
      setData(country);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch country');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [code]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching celebrations with filters
export function useCelebrations(filters?: {
  country?: string;
  year?: number;
  category?: string;
  limit?: number;
  offset?: number;
  search?: string;
}): LoadingState<CelebrationWithDetails[]> {
  const [data, setData] = useState<CelebrationWithDetails[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const celebrations = await getCelebrations(filters);
      setData(celebrations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch celebrations');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching a single celebration by ID
export function useCelebrationById(id: string): LoadingState<CelebrationWithDetails> {
  const [data, setData] = useState<CelebrationWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const celebration = await getCelebrationById(id);
      setData(celebration);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch celebration');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for searching celebrations
export function useSearchCelebrations(query: string, filters?: {
  country?: string;
  year?: number;
  category?: string;
  limit?: number;
}): LoadingState<CelebrationWithDetails[]> {
  const [data, setData] = useState<CelebrationWithDetails[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!query.trim()) {
      setData([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const celebrations = await searchCelebrations(query, filters);
      setData(celebrations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search celebrations');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [query, filters]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching upcoming celebrations
export function useUpcomingCelebrations(limit: number = 10): LoadingState<CelebrationWithDetails[]> {
  const [data, setData] = useState<CelebrationWithDetails[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const celebrations = await getUpcomingCelebrations(limit);
      setData(celebrations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch upcoming celebrations');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching celebration statistics
export function useCelebrationStats(): LoadingState<{
  totalCountries: number;
  totalCelebrations: number;
  categoryCounts: Record<string, number>;
}> {
  const [data, setData] = useState<{
    totalCountries: number;
    totalCelebrations: number;
    categoryCounts: Record<string, number>;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const stats = await getCelebrationStats();
      setData(stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch celebration statistics');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Utility function for handling async operations with loading states
export async function withLoadingState<T>(
  operation: () => Promise<T>,
  setLoading: (loading: boolean) => void,
  setError: (error: string | null) => void
): Promise<T | null> {
  try {
    setLoading(true);
    setError(null);
    const result = await operation();
    return result;
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    return null;
  } finally {
    setLoading(false);
  }
}
