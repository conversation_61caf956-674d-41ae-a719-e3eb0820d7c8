import { Client } from '@notionhq/client';
import { Country, Celebration, MediaContent } from '@/types';

// Environment variables
const notionToken = process.env.NOTION_API_TOKEN!;
const countriesDbId = process.env.NOTION_COUNTRIES_DATABASE_ID!;
const celebrationsDbId = process.env.NOTION_CELEBRATIONS_DATABASE_ID!;
const mediaDbId = process.env.NOTION_MEDIA_DATABASE_ID!;

// Validate environment variables
if (!notionToken) {
  throw new Error('Missing NOTION_API_TOKEN environment variable');
}

if (!countriesDbId || !celebrationsDbId || !mediaDbId) {
  throw new Error('Missing Notion database ID environment variables');
}

// Create Notion client
export const notion = new Client({
  auth: notionToken,
});

// Database IDs
export const DATABASE_IDS = {
  countries: countriesDbId,
  celebrations: celebrationsDbId,
  media: mediaDbId,
};

// Enhanced types for database responses
export interface CountryWithCelebrations extends Country {
  celebrations?: Celebration[];
  celebrationCount?: number;
}

export interface CelebrationWithDetails extends Celebration {
  country: Country;
  media_content: MediaContent[];
}

// Helper function to extract text from Notion rich text
function extractText(richText: any[]): string {
  if (!richText || !Array.isArray(richText)) return '';
  return richText.map(text => text.plain_text).join('');
}

// Helper function to extract array from Notion multi-select
function extractMultiSelect(multiSelect: any[]): string[] {
  if (!multiSelect || !Array.isArray(multiSelect)) return [];
  return multiSelect.map(item => item.name);
}

// Helper function to extract date from Notion date property
function extractDate(dateProperty: any): string {
  if (!dateProperty || !dateProperty.start) return '';
  return dateProperty.start;
}

// Helper function to extract number from Notion number property
function extractNumber(numberProperty: any): number | undefined {
  if (!numberProperty || typeof numberProperty !== 'number') return undefined;
  return numberProperty;
}

// Helper function to extract URL from Notion URL property
function extractUrl(urlProperty: any): string | undefined {
  if (!urlProperty || typeof urlProperty !== 'string') return undefined;
  return urlProperty;
}

// Helper function to convert Notion page to Country
function notionPageToCountry(page: any): Country {
  const properties = page.properties;
  
  return {
    id: page.id,
    name: extractText(properties.Name?.title || []),
    iso_code: extractText(properties.ISO_Code?.rich_text || []),
    description: extractText(properties.Description?.rich_text || []),
    capital: extractText(properties.Capital?.rich_text || []),
    population: extractNumber(properties.Population?.number),
    area: extractNumber(properties.Area?.number),
    languages: extractMultiSelect(properties.Languages?.multi_select || []),
    currency: extractText(properties.Currency?.rich_text || []),
    timezone: extractText(properties.Timezone?.rich_text || []),
    flag_url: extractUrl(properties.Flag_URL?.url),
    created_at: page.created_time,
    updated_at: page.last_edited_time,
  };
}

// Helper function to convert Notion page to Celebration
function notionPageToCelebration(page: any): Celebration {
  const properties = page.properties;
  
  return {
    id: page.id,
    title: extractText(properties.Title?.title || []),
    description: extractText(properties.Description?.rich_text || []),
    date: extractDate(properties.Date?.date),
    category: extractText(properties.Category?.select?.name || []) as Celebration['category'],
    country_id: properties.Country?.relation?.[0]?.id || '',
    tags: extractMultiSelect(properties.Tags?.multi_select || []),
    significance: extractText(properties.Significance?.rich_text || []),
    traditions: extractMultiSelect(properties.Traditions?.multi_select || []),
    created_at: page.created_time,
    updated_at: page.last_edited_time,
  };
}

// Helper function to convert Notion page to MediaContent
function notionPageToMediaContent(page: any): MediaContent {
  const properties = page.properties;
  
  return {
    id: page.id,
    celebration_id: properties.Celebration?.relation?.[0]?.id || '',
    type: extractText(properties.Type?.select?.name || []) as MediaContent['type'],
    url: extractUrl(properties.URL?.url) || '',
    title: extractText(properties.Title?.title || []),
    description: extractText(properties.Description?.rich_text || []),
    thumbnail_url: extractUrl(properties.Thumbnail_URL?.url),
    duration: extractNumber(properties.Duration?.number),
    created_at: page.created_time,
    updated_at: page.last_edited_time,
  };
}

// Database helper functions with enhanced error handling
export async function getCountries(): Promise<Country[]> {
  try {
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.countries,
      sorts: [
        {
          property: 'Name',
          direction: 'ascending',
        },
      ],
    });

    return response.results.map(notionPageToCountry);
  } catch (error) {
    console.error('Error fetching countries from Notion:', error);
    return [];
  }
}

export async function getCountriesWithCelebrationCount(): Promise<CountryWithCelebrations[]> {
  try {
    const countries = await getCountries();
    
    // For each country, count celebrations
    const countriesWithCount = await Promise.all(
      countries.map(async (country) => {
        const celebrationsResponse = await notion.databases.query({
          database_id: DATABASE_IDS.celebrations,
          filter: {
            property: 'Country',
            relation: {
              contains: country.id,
            },
          },
        });

        return {
          ...country,
          celebrationCount: celebrationsResponse.results.length,
        };
      })
    );

    return countriesWithCount;
  } catch (error) {
    console.error('Error fetching countries with celebration count from Notion:', error);
    return [];
  }
}

export async function getCountryByCode(isoCode: string): Promise<Country | null> {
  try {
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.countries,
      filter: {
        property: 'ISO_Code',
        rich_text: {
          equals: isoCode.toUpperCase(),
        },
      },
    });

    if (response.results.length === 0) {
      return null;
    }

    return notionPageToCountry(response.results[0]);
  } catch (error) {
    console.error('Error fetching country by code from Notion:', error);
    return null;
  }
}

export async function getCelebrationsByCountryCode(isoCode: string): Promise<Celebration[]> {
  try {
    // First get the country
    const country = await getCountryByCode(isoCode);
    if (!country) {
      return [];
    }

    // Then get celebrations for that country
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.celebrations,
      filter: {
        property: 'Country',
        relation: {
          contains: country.id,
        },
      },
      sorts: [
        {
          property: 'Date',
          direction: 'ascending',
        },
      ],
    });

    return response.results.map(notionPageToCelebration);
  } catch (error) {
    console.error('Error fetching celebrations by country code from Notion:', error);
    return [];
  }
}

export async function getCelebrations(): Promise<Celebration[]> {
  try {
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.celebrations,
      sorts: [
        {
          property: 'Date',
          direction: 'ascending',
        },
      ],
    });

    return response.results.map(notionPageToCelebration);
  } catch (error) {
    console.error('Error fetching celebrations from Notion:', error);
    return [];
  }
}

export async function getCelebrationById(id: string): Promise<CelebrationWithDetails | null> {
  try {
    // Get the celebration page
    const celebrationPage = await notion.pages.retrieve({ page_id: id });
    const celebration = notionPageToCelebration(celebrationPage);

    // Get the country details
    let country: Country | null = null;
    if (celebration.country_id) {
      const countryPage = await notion.pages.retrieve({ page_id: celebration.country_id });
      country = notionPageToCountry(countryPage);
    }

    // Get media content for this celebration
    const mediaResponse = await notion.databases.query({
      database_id: DATABASE_IDS.media,
      filter: {
        property: 'Celebration',
        relation: {
          contains: id,
        },
      },
    });

    const media_content = mediaResponse.results.map(notionPageToMediaContent);

    return {
      ...celebration,
      country: country!,
      media_content,
    };
  } catch (error) {
    console.error('Error fetching celebration by ID from Notion:', error);
    return null;
  }
}

export async function getUpcomingCelebrations(limit: number = 10): Promise<Celebration[]> {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.celebrations,
      filter: {
        property: 'Date',
        date: {
          on_or_after: today,
        },
      },
      sorts: [
        {
          property: 'Date',
          direction: 'ascending',
        },
      ],
      page_size: limit,
    });

    return response.results.map(notionPageToCelebration);
  } catch (error) {
    console.error('Error fetching upcoming celebrations from Notion:', error);
    return [];
  }
}

export async function getCelebrationStats() {
  try {
    const [countriesResponse, celebrationsResponse] = await Promise.all([
      notion.databases.query({ database_id: DATABASE_IDS.countries }),
      notion.databases.query({ database_id: DATABASE_IDS.celebrations }),
    ]);

    return {
      totalCountries: countriesResponse.results.length,
      totalCelebrations: celebrationsResponse.results.length,
    };
  } catch (error) {
    console.error('Error fetching celebration stats from Notion:', error);
    return {
      totalCountries: 0,
      totalCelebrations: 0,
    };
  }
}

export async function searchCelebrations(query: string): Promise<Celebration[]> {
  try {
    const response = await notion.databases.query({
      database_id: DATABASE_IDS.celebrations,
      filter: {
        or: [
          {
            property: 'Title',
            title: {
              contains: query,
            },
          },
          {
            property: 'Description',
            rich_text: {
              contains: query,
            },
          },
        ],
      },
      sorts: [
        {
          property: 'Date',
          direction: 'ascending',
        },
      ],
    });

    return response.results.map(notionPageToCelebration);
  } catch (error) {
    console.error('Error searching celebrations in Notion:', error);
    return [];
  }
}
