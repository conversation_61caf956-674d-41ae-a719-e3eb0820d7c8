import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
export async function getCountries() {
  const { data, error } = await supabase
    .from('countries')
    .select('*')
    .order('name');
  
  if (error) {
    console.error('Error fetching countries:', error);
    return [];
  }
  
  return data || [];
}

export async function getCelebrations(filters?: {
  country?: string;
  year?: number;
  limit?: number;
  offset?: number;
}) {
  let query = supabase
    .from('celebrations')
    .select(`
      *,
      country:countries(*),
      media_content:media_content(*)
    `)
    .eq('approval_status', 'approved')
    .order('date', { ascending: false });

  if (filters?.country) {
    query = query.eq('country_id', filters.country);
  }

  if (filters?.year) {
    const startDate = `${filters.year}-01-01`;
    const endDate = `${filters.year}-12-31`;
    query = query.gte('date', startDate).lte('date', endDate);
  }

  if (filters?.limit) {
    query = query.limit(filters.limit);
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching celebrations:', error);
    return [];
  }

  return data || [];
}

export async function getCelebrationById(id: string) {
  const { data, error } = await supabase
    .from('celebrations')
    .select(`
      *,
      country:countries(*),
      media_content:media_content(*)
    `)
    .eq('id', id)
    .eq('approval_status', 'approved')
    .single();

  if (error) {
    console.error('Error fetching celebration:', error);
    return null;
  }

  return data;
}

export async function searchCelebrations(query: string, filters?: {
  country?: string;
  year?: number;
  limit?: number;
}) {
  let searchQuery = supabase
    .from('celebrations')
    .select(`
      *,
      country:countries(*),
      media_content:media_content(*)
    `)
    .eq('approval_status', 'approved')
    .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
    .order('date', { ascending: false });

  if (filters?.country) {
    searchQuery = searchQuery.eq('country_id', filters.country);
  }

  if (filters?.year) {
    const startDate = `${filters.year}-01-01`;
    const endDate = `${filters.year}-12-31`;
    searchQuery = searchQuery.gte('date', startDate).lte('date', endDate);
  }

  if (filters?.limit) {
    searchQuery = searchQuery.limit(filters.limit);
  }

  const { data, error } = await searchQuery;

  if (error) {
    console.error('Error searching celebrations:', error);
    return [];
  }

  return data || [];
}
