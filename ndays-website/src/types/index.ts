// Database types
export interface Country {
  id: string;
  name: string;
  iso_code: string;
  flag_url?: string;
  timezone: string;
  created_at: string;
  updated_at: string;
}

export interface Celebration {
  id: string;
  title: string;
  description: string;
  date: string;
  country_id: string;
  country?: Country;
  media_content?: MediaContent[];
  tags?: string[];
  category?: string;
  approval_status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface MediaContent {
  id: string;
  url: string;
  type: 'youtube' | 'image' | 'video';
  source: string;
  attribution?: string;
  celebration_id: string;
  celebration?: Celebration;
  thumbnail_url?: string;
  title?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
  embedUrl: string;
}

export interface GeolocationResponse {
  country: string;
  countryCode: string;
  timezone: string;
  success: boolean;
}

// Component props types
export interface CelebrationCardProps {
  celebration: Celebration;
  showCountry?: boolean;
}

export interface CountryFilterProps {
  countries: Country[];
  selectedCountry?: string;
  onCountryChange: (countryId: string) => void;
}

export interface SearchFilters {
  country?: string;
  year?: number;
  category?: string;
  query?: string;
}

// Form types
export interface CelebrationFormData {
  title: string;
  description: string;
  date: string;
  country_id: string;
  tags: string[];
  category: string;
}

export interface MediaFormData {
  url: string;
  type: 'youtube' | 'image' | 'video';
  source: string;
  attribution?: string;
  celebration_id: string;
}

// Admin types
export interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'moderator';
  created_at: string;
}

export interface ContentModerationAction {
  id: string;
  celebration_id: string;
  admin_id: string;
  action: 'approve' | 'reject' | 'edit';
  reason?: string;
  created_at: string;
}
