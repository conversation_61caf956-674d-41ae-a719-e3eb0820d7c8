(()=>{var e={"./node_modules/.pnpm/mlly@1.8.0/node_modules/mlly/dist lazy recursive":function(e){function webpackEmptyAsyncContext(e){return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t})}webpackEmptyAsyncContext.keys=()=>[],webpackEmptyAsyncContext.resolve=webpackEmptyAsyncContext,webpackEmptyAsyncContext.id="./node_modules/.pnpm/mlly@1.8.0/node_modules/mlly/dist lazy recursive",e.exports=webpackEmptyAsyncContext}},t={};function __webpack_require__(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,__webpack_require__),r.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var i in t)__webpack_require__.o(t,i)&&!__webpack_require__.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i={};(()=>{"use strict";let e,t,s;__webpack_require__.d(i,{default:()=>createJiti});let r=require("node:os");var n,a=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239],o=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],h="\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲊᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟍꟐꟑꟓꟕ-Ƛꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",c={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},p="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",l={5:p,"5module":p+" export import",6:p+" const class extends export import super"},u=/^in(stanceof)?$/,d=RegExp("["+h+"]"),f=RegExp("["+h+"‌‍\xb7̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࢗ-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･]");function isInAstralSet(e,t){for(var i=65536,s=0;s<t.length&&!((i+=t[s])>e);s+=2)if((i+=t[s+1])>=e)return!0;return!1}function isIdentifierStart(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&d.test(String.fromCharCode(e)):!1!==t&&isInAstralSet(e,o)))}function isIdentifierChar(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&f.test(String.fromCharCode(e)):!1!==t&&(isInAstralSet(e,o)||isInAstralSet(e,a)))))}var acorn_TokenType=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function binop(e,t){return new acorn_TokenType(e,{beforeExpr:!0,binop:t})}var m={beforeExpr:!0},g={startsExpr:!0},_={};function kw(e,t){return void 0===t&&(t={}),t.keyword=e,_[e]=new acorn_TokenType(e,t)}var x={num:new acorn_TokenType("num",g),regexp:new acorn_TokenType("regexp",g),string:new acorn_TokenType("string",g),name:new acorn_TokenType("name",g),privateId:new acorn_TokenType("privateId",g),eof:new acorn_TokenType("eof"),bracketL:new acorn_TokenType("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new acorn_TokenType("]"),braceL:new acorn_TokenType("{",{beforeExpr:!0,startsExpr:!0}),braceR:new acorn_TokenType("}"),parenL:new acorn_TokenType("(",{beforeExpr:!0,startsExpr:!0}),parenR:new acorn_TokenType(")"),comma:new acorn_TokenType(",",m),semi:new acorn_TokenType(";",m),colon:new acorn_TokenType(":",m),dot:new acorn_TokenType("."),question:new acorn_TokenType("?",m),questionDot:new acorn_TokenType("?."),arrow:new acorn_TokenType("=>",m),template:new acorn_TokenType("template"),invalidTemplate:new acorn_TokenType("invalidTemplate"),ellipsis:new acorn_TokenType("...",m),backQuote:new acorn_TokenType("`",g),dollarBraceL:new acorn_TokenType("${",{beforeExpr:!0,startsExpr:!0}),eq:new acorn_TokenType("=",{beforeExpr:!0,isAssign:!0}),assign:new acorn_TokenType("_=",{beforeExpr:!0,isAssign:!0}),incDec:new acorn_TokenType("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new acorn_TokenType("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:binop("||",1),logicalAND:binop("&&",2),bitwiseOR:binop("|",3),bitwiseXOR:binop("^",4),bitwiseAND:binop("&",5),equality:binop("==/!=/===/!==",6),relational:binop("</>/<=/>=",7),bitShift:binop("<</>>/>>>",8),plusMin:new acorn_TokenType("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:binop("%",10),star:binop("*",10),slash:binop("/",10),starstar:new acorn_TokenType("**",{beforeExpr:!0}),coalesce:binop("??",1),_break:kw("break"),_case:kw("case",m),_catch:kw("catch"),_continue:kw("continue"),_debugger:kw("debugger"),_default:kw("default",m),_do:kw("do",{isLoop:!0,beforeExpr:!0}),_else:kw("else",m),_finally:kw("finally"),_for:kw("for",{isLoop:!0}),_function:kw("function",g),_if:kw("if"),_return:kw("return",m),_switch:kw("switch"),_throw:kw("throw",m),_try:kw("try"),_var:kw("var"),_const:kw("const"),_while:kw("while",{isLoop:!0}),_with:kw("with"),_new:kw("new",{beforeExpr:!0,startsExpr:!0}),_this:kw("this",g),_super:kw("super",g),_class:kw("class",g),_extends:kw("extends",m),_export:kw("export"),_import:kw("import",g),_null:kw("null",g),_true:kw("true",g),_false:kw("false",g),_in:kw("in",{beforeExpr:!0,binop:7}),_instanceof:kw("instanceof",{beforeExpr:!0,binop:7}),_typeof:kw("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:kw("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:kw("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},v=/\r\n?|\n|\u2028|\u2029/,y=RegExp(v.source,"g");function isNewLine(e){return 10===e||13===e||8232===e||8233===e}function nextLineBreak(e,t,i){void 0===i&&(i=e.length);for(var s=t;s<i;s++){var r=e.charCodeAt(s);if(isNewLine(r))return s<i-1&&13===r&&10===e.charCodeAt(s+1)?s+2:s+1}return -1}var b=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,E=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,k=Object.prototype,S=k.hasOwnProperty,C=k.toString,w=Object.hasOwn||function(e,t){return S.call(e,t)},T=Array.isArray||function(e){return"[object Array]"===C.call(e)},I=Object.create(null);function wordsRegexp(e){return I[e]||(I[e]=RegExp("^(?:"+e.replace(/ /g,"|")+")$"))}function codePointToString(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,(1023&e)+56320)}var R=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,acorn_Position=function(e,t){this.line=e,this.column=t};acorn_Position.prototype.offset=function(e){return new acorn_Position(this.line,this.column+e)};var acorn_SourceLocation=function(e,t,i){this.start=t,this.end=i,null!==e.sourceFile&&(this.source=e.sourceFile)};function getLineInfo(e,t){for(var i=1,s=0;;){var r=nextLineBreak(e,s,t);if(r<0)return new acorn_Position(i,t-s);++i,s=r}}var P={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,checkPrivateFields:!0,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},A=!1;function getOptions(e){var t={};for(var i in P)t[i]=e&&w(e,i)?e[i]:P[i];if("latest"===t.ecmaVersion?t.ecmaVersion=1e8:null==t.ecmaVersion?(!A&&"object"==typeof console&&console.warn&&(A=!0,console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),e&&null!=e.allowHashBang||(t.allowHashBang=t.ecmaVersion>=14),T(t.onToken)){var s=t.onToken;t.onToken=function(e){return s.push(e)}}return T(t.onComment)&&(t.onComment=pushComment(t,t.onComment)),t}function pushComment(e,t){return function(i,s,r,n,a,o){var h={type:i?"Block":"Line",value:s,start:r,end:n};e.locations&&(h.loc=new acorn_SourceLocation(this,a,o)),e.ranges&&(h.range=[r,n]),t.push(h)}}function functionFlags(e,t){return 2|4*!!e|8*!!t}var acorn_Parser=function(e,t,i){this.options=e=getOptions(e),this.sourceFile=e.sourceFile,this.keywords=wordsRegexp(l[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var s="";!0!==e.allowReserved&&(s=c[e.ecmaVersion>=6?6:5===e.ecmaVersion?5:3],"module"===e.sourceType&&(s+=" await")),this.reservedWords=wordsRegexp(s);var r=(s?s+" ":"")+c.strict;this.reservedWordsStrict=wordsRegexp(r),this.reservedWordsStrictBind=wordsRegexp(r+" "+c.strictBind),this.input=String(t),this.containsEsc=!1,i?(this.pos=i,this.lineStart=this.input.lastIndexOf("\n",i-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(v).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=x.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null,this.privateNameStack=[]},N={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};acorn_Parser.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},N.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},N.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0},N.inAsync.get=function(){return(4&this.currentVarScope().flags)>0},N.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e].flags;if(768&t)return!1;if(2&t)return(4&t)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},N.allowSuper.get=function(){return(64&this.currentThisScope().flags)>0||this.options.allowSuperOutsideMethod},N.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},N.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},N.allowNewDotTarget.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e].flags;if(768&t||2&t&&!(16&t))return!0}return!1},N.inClassStaticBlock.get=function(){return(256&this.currentVarScope().flags)>0},acorn_Parser.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var i=this,s=0;s<e.length;s++)i=e[s](i);return i},acorn_Parser.parse=function(e,t){return new this(t,e).parse()},acorn_Parser.parseExpressionAt=function(e,t,i){var s=new this(i,e,t);return s.nextToken(),s.parseExpression()},acorn_Parser.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(acorn_Parser.prototype,N);var L=acorn_Parser.prototype,D=/^(?:'((?:\\[^]|[^'\\])*?)'|"((?:\\[^]|[^"\\])*?)")/;L.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){E.lastIndex=e,e+=E.exec(this.input)[0].length;var t=D.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){E.lastIndex=e+t[0].length;var i=E.exec(this.input),s=i.index+i[0].length,r=this.input.charAt(s);return";"===r||"}"===r||v.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(r)||"!"===r&&"="===this.input.charAt(s+1))}E.lastIndex=e+=t[0].length,e+=E.exec(this.input)[0].length,";"===this.input[e]&&e++}},L.eat=function(e){return this.type===e&&(this.next(),!0)},L.isContextual=function(e){return this.type===x.name&&this.value===e&&!this.containsEsc},L.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},L.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},L.canInsertSemicolon=function(){return this.type===x.eof||this.type===x.braceR||v.test(this.input.slice(this.lastTokEnd,this.start))},L.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},L.semicolon=function(){this.eat(x.semi)||this.insertSemicolon()||this.unexpected()},L.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},L.expect=function(e){this.eat(e)||this.unexpected()},L.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var acorn_DestructuringErrors=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};L.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var i=t?e.parenthesizedAssign:e.parenthesizedBind;i>-1&&this.raiseRecoverable(i,t?"Assigning to rvalue":"Parenthesized pattern")}},L.checkExpressionErrors=function(e,t){if(!e)return!1;var i=e.shorthandAssign,s=e.doubleProto;if(!t)return i>=0||s>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},L.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},L.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var O=acorn_Parser.prototype;O.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==x.eof;){var i=this.parseStatement(null,!0,t);e.body.push(i)}if(this.inModule)for(var s=0,r=Object.keys(this.undefinedExports);s<r.length;s+=1){var n=r[s];this.raiseRecoverable(this.undefinedExports[n].start,"Export '"+n+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var V={kind:"loop"},U={kind:"switch"};O.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;E.lastIndex=this.pos;var t=E.exec(this.input),i=this.pos+t[0].length,s=this.input.charCodeAt(i);if(91===s||92===s)return!0;if(e)return!1;if(123===s||s>55295&&s<56320)return!0;if(isIdentifierStart(s,!0)){for(var r=i+1;isIdentifierChar(s=this.input.charCodeAt(r),!0);)++r;if(92===s||s>55295&&s<56320)return!0;var n=this.input.slice(i,r);if(!u.test(n))return!0}return!1},O.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;E.lastIndex=this.pos;var e,t=E.exec(this.input),i=this.pos+t[0].length;return!v.test(this.input.slice(this.pos,i))&&"function"===this.input.slice(i,i+8)&&(i+8===this.input.length||!(isIdentifierChar(e=this.input.charCodeAt(i+8))||e>55295&&e<56320))},O.isUsingKeyword=function(e,t){if(this.options.ecmaVersion<17||!this.isContextual(e?"await":"using"))return!1;E.lastIndex=this.pos;var i=E.exec(this.input),s=this.pos+i[0].length;if(v.test(this.input.slice(this.pos,s)))return!1;if(e){var r,n=s+5;if("using"!==this.input.slice(s,n)||n===this.input.length||isIdentifierChar(r=this.input.charCodeAt(n))||r>55295&&r<56320)return!1;E.lastIndex=n;var a=E.exec(this.input);if(a&&v.test(this.input.slice(n,n+a[0].length)))return!1}if(t){var o,h=s+2;if("of"===this.input.slice(s,h)&&(h===this.input.length||!isIdentifierChar(o=this.input.charCodeAt(h))&&!(o>55295&&o<56320)))return!1}var c=this.input.charCodeAt(s);return isIdentifierStart(c,!0)||92===c},O.isAwaitUsing=function(e){return this.isUsingKeyword(!0,e)},O.isUsing=function(e){return this.isUsingKeyword(!1,e)},O.parseStatement=function(e,t,i){var s,r=this.type,n=this.startNode();switch(this.isLet(e)&&(r=x._var,s="let"),r){case x._break:case x._continue:return this.parseBreakContinueStatement(n,r.keyword);case x._debugger:return this.parseDebuggerStatement(n);case x._do:return this.parseDoStatement(n);case x._for:return this.parseForStatement(n);case x._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(n,!1,!e);case x._class:return e&&this.unexpected(),this.parseClass(n,!0);case x._if:return this.parseIfStatement(n);case x._return:return this.parseReturnStatement(n);case x._switch:return this.parseSwitchStatement(n);case x._throw:return this.parseThrowStatement(n);case x._try:return this.parseTryStatement(n);case x._const:case x._var:return s=s||this.value,e&&"var"!==s&&this.unexpected(),this.parseVarStatement(n,s);case x._while:return this.parseWhileStatement(n);case x._with:return this.parseWithStatement(n);case x.braceL:return this.parseBlock(!0,n);case x.semi:return this.parseEmptyStatement(n);case x._export:case x._import:if(this.options.ecmaVersion>10&&r===x._import){E.lastIndex=this.pos;var a=E.exec(this.input),o=this.pos+a[0].length,h=this.input.charCodeAt(o);if(40===h||46===h)return this.parseExpressionStatement(n,this.parseExpression())}return!this.options.allowImportExportEverywhere&&(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),r===x._import?this.parseImport(n):this.parseExport(n,i);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(n,!0,!e);var c=this.isAwaitUsing(!1)?"await using":this.isUsing(!1)?"using":null;if(c)return t&&"script"===this.options.sourceType&&this.raise(this.start,"Using declaration cannot appear in the top level when source type is `script`"),"await using"===c&&(this.canAwait||this.raise(this.start,"Await using cannot appear outside of async function"),this.next()),this.next(),this.parseVar(n,!1,c),this.semicolon(),this.finishNode(n,"VariableDeclaration");var p=this.value,l=this.parseExpression();if(r===x.name&&"Identifier"===l.type&&this.eat(x.colon))return this.parseLabeledStatement(n,p,l,e);return this.parseExpressionStatement(n,l)}},O.parseBreakContinueStatement=function(e,t){var i="break"===t;this.next(),this.eat(x.semi)||this.insertSemicolon()?e.label=null:this.type!==x.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var r=this.labels[s];if((null==e.label||r.name===e.label.name)&&(null!=r.kind&&(i||"loop"===r.kind)||e.label&&i))break}return s===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,i?"BreakStatement":"ContinueStatement")},O.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},O.parseDoStatement=function(e){return this.next(),this.labels.push(V),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(x._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(x.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},O.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(V),this.enterScope(0),this.expect(x.parenL),this.type===x.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var i=this.isLet();if(this.type===x._var||this.type===x._const||i){var s=this.startNode(),r=i?"let":this.value;return this.next(),this.parseVar(s,!0,r),this.finishNode(s,"VariableDeclaration"),this.parseForAfterInit(e,s,t)}var n=this.isContextual("let"),a=!1,o=this.isUsing(!0)?"using":this.isAwaitUsing(!0)?"await using":null;if(o){var h=this.startNode();return this.next(),"await using"===o&&this.next(),this.parseVar(h,!0,o),this.finishNode(h,"VariableDeclaration"),this.parseForAfterInit(e,h,t)}var c=this.containsEsc,p=new acorn_DestructuringErrors,l=this.start,u=t>-1?this.parseExprSubscripts(p,"await"):this.parseExpression(!0,p);return this.type===x._in||(a=this.options.ecmaVersion>=6&&this.isContextual("of"))?(t>-1?(this.type===x._in&&this.unexpected(t),e.await=!0):a&&this.options.ecmaVersion>=8&&(u.start!==l||c||"Identifier"!==u.type||"async"!==u.name?this.options.ecmaVersion>=9&&(e.await=!1):this.unexpected()),n&&a&&this.raise(u.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(u,!1,p),this.checkLValPattern(u),this.parseForIn(e,u)):(this.checkExpressionErrors(p,!0),t>-1&&this.unexpected(t),this.parseFor(e,u))},O.parseForAfterInit=function(e,t,i){return(this.type===x._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===t.declarations.length?(this.options.ecmaVersion>=9&&(this.type===x._in?i>-1&&this.unexpected(i):e.await=i>-1),this.parseForIn(e,t)):(i>-1&&this.unexpected(i),this.parseFor(e,t))},O.parseFunctionStatement=function(e,t,i){return this.next(),this.parseFunction(e,j|(i?0:B),!1,t)},O.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(x._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},O.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(x.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},O.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(x.braceL),this.labels.push(U),this.enterScope(0);for(var t,i=!1;this.type!==x.braceR;)if(this.type===x._case||this.type===x._default){var s=this.type===x._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,t.test=null),this.expect(x.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},O.parseThrowStatement=function(e){return this.next(),v.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var M=[];O.parseCatchClauseParam=function(){var e=this.parseBindingAtom(),t="Identifier"===e.type;return this.enterScope(32*!!t),this.checkLValPattern(e,t?4:2),this.expect(x.parenR),e},O.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===x._catch){var t=this.startNode();this.next(),this.eat(x.parenL)?t.param=this.parseCatchClauseParam():(this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0)),t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(x._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},O.parseVarStatement=function(e,t,i){return this.next(),this.parseVar(e,!1,t,i),this.semicolon(),this.finishNode(e,"VariableDeclaration")},O.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(V),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},O.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},O.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},O.parseLabeledStatement=function(e,t,i,s){for(var r=0,n=this.labels;r<n.length;r+=1)n[r].name===t&&this.raise(i.start,"Label '"+t+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===x._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var h=this.labels[o];if(h.statementStart===e.start)h.statementStart=this.start,h.kind=a;else break}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(s?-1===s.indexOf("label")?s+"label":s:"label"),this.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")},O.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},O.parseBlock=function(e,t,i){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(x.braceL),e&&this.enterScope(0);this.type!==x.braceR;){var s=this.parseStatement(null);t.body.push(s)}return i&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},O.parseFor=function(e,t){return e.init=t,this.expect(x.semi),e.test=this.type===x.semi?null:this.parseExpression(),this.expect(x.semi),e.update=this.type===x.parenR?null:this.parseExpression(),this.expect(x.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},O.parseForIn=function(e,t){var i=this.type===x._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!i||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)&&this.raise(t.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(x.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")},O.parseVar=function(e,t,i,s){for(e.declarations=[],e.kind=i;;){var r=this.startNode();if(this.parseVarId(r,i),this.eat(x.eq)?r.init=this.parseMaybeAssign(t):s||"const"!==i||this.type===x._in||this.options.ecmaVersion>=6&&this.isContextual("of")?s||"using"!==i&&"await using"!==i||!(this.options.ecmaVersion>=17)||this.type===x._in||this.isContextual("of")?s||"Identifier"===r.id.type||t&&(this.type===x._in||this.isContextual("of"))?r.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.raise(this.lastTokEnd,"Missing initializer in "+i+" declaration"):this.unexpected(),e.declarations.push(this.finishNode(r,"VariableDeclarator")),!this.eat(x.comma))break}return e},O.parseVarId=function(e,t){e.id="using"===t||"await using"===t?this.parseIdent():this.parseBindingAtom(),this.checkLValPattern(e.id,"var"===t?1:2,!1)};var j=1,B=2;function isPrivateNameConflicted(e,t){var i=t.key.name,s=e[i],r="true";return("MethodDefinition"===t.type&&("get"===t.kind||"set"===t.kind)&&(r=(t.static?"s":"i")+t.kind),"iget"===s&&"iset"===r||"iset"===s&&"iget"===r||"sget"===s&&"sset"===r||"sset"===s&&"sget"===r)?(e[i]="true",!1):!!s||(e[i]=r,!1)}function checkKeyName(e,t){var i=e.computed,s=e.key;return!i&&("Identifier"===s.type&&s.name===t||"Literal"===s.type&&s.value===t)}O.parseFunction=function(e,t,i,s,r){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===x.star&&t&B&&this.unexpected(),e.generator=this.eat(x.star)),this.options.ecmaVersion>=8&&(e.async=!!s),t&j&&(e.id=4&t&&this.type!==x.name?null:this.parseIdent(),e.id&&!(t&B)&&this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var n=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(functionFlags(e.async,e.generator)),t&j||(e.id=this.type===x.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,i,!1,r),this.yieldPos=n,this.awaitPos=a,this.awaitIdentPos=o,this.finishNode(e,t&j?"FunctionDeclaration":"FunctionExpression")},O.parseFunctionParams=function(e){this.expect(x.parenL),e.params=this.parseBindingList(x.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},O.parseClass=function(e,t){this.next();var i=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var s=this.enterClassBody(),r=this.startNode(),n=!1;for(r.body=[],this.expect(x.braceL);this.type!==x.braceR;){var a=this.parseClassElement(null!==e.superClass);a&&(r.body.push(a),"MethodDefinition"===a.type&&"constructor"===a.kind?(n&&this.raiseRecoverable(a.start,"Duplicate constructor in the same class"),n=!0):a.key&&"PrivateIdentifier"===a.key.type&&isPrivateNameConflicted(s,a)&&this.raiseRecoverable(a.key.start,"Identifier '#"+a.key.name+"' has already been declared"))}return this.strict=i,this.next(),e.body=this.finishNode(r,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},O.parseClassElement=function(e){if(this.eat(x.semi))return null;var t=this.options.ecmaVersion,i=this.startNode(),s="",r=!1,n=!1,a="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(x.braceL))return this.parseClassStaticBlock(i),i;this.isClassElementNameStart()||this.type===x.star?o=!0:s="static"}if(i.static=o,!s&&t>=8&&this.eatContextual("async")&&((this.isClassElementNameStart()||this.type===x.star)&&!this.canInsertSemicolon()?n=!0:s="async"),!s&&(t>=9||!n)&&this.eat(x.star)&&(r=!0),!s&&!n&&!r){var h=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?a=h:s=h)}if(s?(i.computed=!1,i.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),i.key.name=s,this.finishNode(i.key,"Identifier")):this.parseClassElementName(i),t<13||this.type===x.parenL||"method"!==a||r||n){var c=!i.static&&checkKeyName(i,"constructor");c&&"method"!==a&&this.raise(i.key.start,"Constructor can't have get/set modifier"),i.kind=c?"constructor":a,this.parseClassMethod(i,r,n,c&&e)}else this.parseClassField(i);return i},O.isClassElementNameStart=function(){return this.type===x.name||this.type===x.privateId||this.type===x.num||this.type===x.string||this.type===x.bracketL||this.type.keyword},O.parseClassElementName=function(e){this.type===x.privateId?("constructor"===this.value&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},O.parseClassMethod=function(e,t,i,s){var r=e.key;"constructor"===e.kind?(t&&this.raise(r.start,"Constructor can't be a generator"),i&&this.raise(r.start,"Constructor can't be an async method")):e.static&&checkKeyName(e,"prototype")&&this.raise(r.start,"Classes may not have a static property named prototype");var n=e.value=this.parseMethod(t,i,s);return"get"===e.kind&&0!==n.params.length&&this.raiseRecoverable(n.start,"getter should have no params"),"set"===e.kind&&1!==n.params.length&&this.raiseRecoverable(n.start,"setter should have exactly one param"),"set"===e.kind&&"RestElement"===n.params[0].type&&this.raiseRecoverable(n.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},O.parseClassField=function(e){return checkKeyName(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&checkKeyName(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(x.eq)?(this.enterScope(576),e.value=this.parseMaybeAssign(),this.exitScope()):e.value=null,this.semicolon(),this.finishNode(e,"PropertyDefinition")},O.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(320);this.type!==x.braceR;){var i=this.parseStatement(null);e.body.push(i)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},O.parseClassId=function(e,t){this.type===x.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},O.parseClassSuper=function(e){e.superClass=this.eat(x._extends)?this.parseExprSubscripts(null,!1):null},O.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},O.exitClassBody=function(){var e=this.privateNameStack.pop(),t=e.declared,i=e.used;if(this.options.checkPrivateFields)for(var s=this.privateNameStack.length,r=0===s?null:this.privateNameStack[s-1],n=0;n<i.length;++n){var a=i[n];w(t,a.name)||(r?r.used.push(a):this.raiseRecoverable(a.start,"Private field '#"+a.name+"' must be declared in an enclosing class"))}},O.parseExportAllDeclaration=function(e,t){return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==x.string&&this.unexpected(),e.source=this.parseExprAtom(),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause()),this.semicolon(),this.finishNode(e,"ExportAllDeclaration")},O.parseExport=function(e,t){if(this.next(),this.eat(x.star))return this.parseExportAllDeclaration(e,t);if(this.eat(x._default))return this.checkExport(t,"default",this.lastTokStart),e.declaration=this.parseExportDefaultDeclaration(),this.finishNode(e,"ExportDefaultDeclaration");if(this.shouldParseExportStatement())e.declaration=this.parseExportDeclaration(e),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null,this.options.ecmaVersion>=16&&(e.attributes=[]);else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==x.string&&this.unexpected(),e.source=this.parseExprAtom(),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause());else{for(var i=0,s=e.specifiers;i<s.length;i+=1){var r=s[i];this.checkUnreserved(r.local),this.checkLocalExport(r.local),"Literal"===r.local.type&&this.raise(r.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null,this.options.ecmaVersion>=16&&(e.attributes=[])}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},O.parseExportDeclaration=function(e){return this.parseStatement(null)},O.parseExportDefaultDeclaration=function(){if(this.type===x._function||(e=this.isAsyncFunction())){var e,t=this.startNode();return this.next(),e&&this.next(),this.parseFunction(t,4|j,!1,e)}if(this.type===x._class){var i=this.startNode();return this.parseClass(i,"nullableID")}var s=this.parseMaybeAssign();return this.semicolon(),s},O.checkExport=function(e,t,i){e&&("string"!=typeof t&&(t="Identifier"===t.type?t.name:t.value),w(e,t)&&this.raiseRecoverable(i,"Duplicate export '"+t+"'"),e[t]=!0)},O.checkPatternExport=function(e,t){var i=t.type;if("Identifier"===i)this.checkExport(e,t,t.start);else if("ObjectPattern"===i)for(var s=0,r=t.properties;s<r.length;s+=1){var n=r[s];this.checkPatternExport(e,n)}else if("ArrayPattern"===i)for(var a=0,o=t.elements;a<o.length;a+=1){var h=o[a];h&&this.checkPatternExport(e,h)}else"Property"===i?this.checkPatternExport(e,t.value):"AssignmentPattern"===i?this.checkPatternExport(e,t.left):"RestElement"===i&&this.checkPatternExport(e,t.argument)},O.checkVariableExport=function(e,t){if(e)for(var i=0;i<t.length;i+=1){var s=t[i];this.checkPatternExport(e,s.id)}},O.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},O.parseExportSpecifier=function(e){var t=this.startNode();return t.local=this.parseModuleExportName(),t.exported=this.eatContextual("as")?this.parseModuleExportName():t.local,this.checkExport(e,t.exported,t.exported.start),this.finishNode(t,"ExportSpecifier")},O.parseExportSpecifiers=function(e){var t=[],i=!0;for(this.expect(x.braceL);!this.eat(x.braceR);){if(i)i=!1;else if(this.expect(x.comma),this.afterTrailingComma(x.braceR))break;t.push(this.parseExportSpecifier(e))}return t},O.parseImport=function(e){return this.next(),this.type===x.string?(e.specifiers=M,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===x.string?this.parseExprAtom():this.unexpected()),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},O.parseImportSpecifier=function(){var e=this.startNode();return e.imported=this.parseModuleExportName(),this.eatContextual("as")?e.local=this.parseIdent():(this.checkUnreserved(e.imported),e.local=e.imported),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportSpecifier")},O.parseImportDefaultSpecifier=function(){var e=this.startNode();return e.local=this.parseIdent(),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportDefaultSpecifier")},O.parseImportNamespaceSpecifier=function(){var e=this.startNode();return this.next(),this.expectContextual("as"),e.local=this.parseIdent(),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportNamespaceSpecifier")},O.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===x.name&&(e.push(this.parseImportDefaultSpecifier()),!this.eat(x.comma)))return e;if(this.type===x.star)return e.push(this.parseImportNamespaceSpecifier()),e;for(this.expect(x.braceL);!this.eat(x.braceR);){if(t)t=!1;else if(this.expect(x.comma),this.afterTrailingComma(x.braceR))break;e.push(this.parseImportSpecifier())}return e},O.parseWithClause=function(){var e=[];if(!this.eat(x._with))return e;this.expect(x.braceL);for(var t={},i=!0;!this.eat(x.braceR);){if(i)i=!1;else if(this.expect(x.comma),this.afterTrailingComma(x.braceR))break;var s=this.parseImportAttribute(),r="Identifier"===s.key.type?s.key.name:s.key.value;w(t,r)&&this.raiseRecoverable(s.key.start,"Duplicate attribute key '"+r+"'"),t[r]=!0,e.push(s)}return e},O.parseImportAttribute=function(){var e=this.startNode();return e.key=this.type===x.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved),this.expect(x.colon),this.type!==x.string&&this.unexpected(),e.value=this.parseExprAtom(),this.finishNode(e,"ImportAttribute")},O.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===x.string){var e=this.parseLiteral(this.value);return R.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},O.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},O.isDirectiveCandidate=function(e){return this.options.ecmaVersion>=5&&"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var F=acorn_Parser.prototype;F.toAssignable=function(e,t,i){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var s=0,r=e.properties;s<r.length;s+=1){var n=r[s];this.toAssignable(n,t),"RestElement"===n.type&&("ArrayPattern"===n.argument.type||"ObjectPattern"===n.argument.type)&&this.raise(n.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,i);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return e},F.toAssignableList=function(e,t){for(var i=e.length,s=0;s<i;s++){var r=e[s];r&&this.toAssignable(r,t)}if(i){var n=e[i-1];6===this.options.ecmaVersion&&t&&n&&"RestElement"===n.type&&"Identifier"!==n.argument.type&&this.unexpected(n.argument.start)}return e},F.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},F.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==x.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},F.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case x.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(x.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case x.braceL:return this.parseObj(!0)}return this.parseIdent()},F.parseBindingList=function(e,t,i,s){for(var r=[],n=!0;!this.eat(e);)if(n?n=!1:this.expect(x.comma),t&&this.type===x.comma)r.push(null);else if(i&&this.afterTrailingComma(e))break;else if(this.type===x.ellipsis){var a=this.parseRestBinding();this.parseBindingListItem(a),r.push(a),this.type===x.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}else r.push(this.parseAssignableListItem(s));return r},F.parseAssignableListItem=function(e){var t=this.parseMaybeDefault(this.start,this.startLoc);return this.parseBindingListItem(t),t},F.parseBindingListItem=function(e){return e},F.parseMaybeDefault=function(e,t,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(x.eq))return i;var s=this.startNodeAt(e,t);return s.left=i,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},F.checkLValSimple=function(e,t,i){void 0===t&&(t=0);var s=0!==t;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode"),s&&(2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),i&&(w(i,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),i[e.name]=!0),5!==t&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":s&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return s&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,i);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}},F.checkLValPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"ObjectPattern":for(var s=0,r=e.properties;s<r.length;s+=1){var n=r[s];this.checkLValInnerPattern(n,t,i)}break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var h=o[a];h&&this.checkLValInnerPattern(h,t,i)}break;default:this.checkLValSimple(e,t,i)}},F.checkLValInnerPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"Property":this.checkLValInnerPattern(e.value,t,i);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,i);break;case"RestElement":this.checkLValPattern(e.argument,t,i);break;default:this.checkLValPattern(e,t,i)}};var acorn_TokContext=function(e,t,i,s,r){this.token=e,this.isExpr=!!t,this.preserveSpace=!!i,this.override=s,this.generator=!!r},$={b_stat:new acorn_TokContext("{",!1),b_expr:new acorn_TokContext("{",!0),b_tmpl:new acorn_TokContext("${",!1),p_stat:new acorn_TokContext("(",!1),p_expr:new acorn_TokContext("(",!0),q_tmpl:new acorn_TokContext("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new acorn_TokContext("function",!1),f_expr:new acorn_TokContext("function",!0),f_expr_gen:new acorn_TokContext("function",!0,!1,null,!0),f_gen:new acorn_TokContext("function",!1,!1,null,!0)},q=acorn_Parser.prototype;q.initialContext=function(){return[$.b_stat]},q.curContext=function(){return this.context[this.context.length-1]},q.braceIsBlock=function(e){var t=this.curContext();return t===$.f_expr||t===$.f_stat||(e===x.colon&&(t===$.b_stat||t===$.b_expr)?!t.isExpr:e===x._return||e===x.name&&this.exprAllowed?v.test(this.input.slice(this.lastTokEnd,this.start)):e===x._else||e===x.semi||e===x.eof||e===x.parenR||e===x.arrow||(e===x.braceL?t===$.b_stat:e!==x._var&&e!==x._const&&e!==x.name&&!this.exprAllowed))},q.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},q.updateContext=function(e){var t,i=this.type;i.keyword&&e===x.dot?this.exprAllowed=!1:(t=i.updateContext)?t.call(this,e):this.exprAllowed=i.beforeExpr},q.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},x.parenR.updateContext=x.braceR.updateContext=function(){if(1===this.context.length){this.exprAllowed=!0;return}var e=this.context.pop();e===$.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr},x.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?$.b_stat:$.b_expr),this.exprAllowed=!0},x.dollarBraceL.updateContext=function(){this.context.push($.b_tmpl),this.exprAllowed=!0},x.parenL.updateContext=function(e){var t=e===x._if||e===x._for||e===x._with||e===x._while;this.context.push(t?$.p_stat:$.p_expr),this.exprAllowed=!0},x.incDec.updateContext=function(){},x._function.updateContext=x._class.updateContext=function(e){e.beforeExpr&&e!==x._else&&(e!==x.semi||this.curContext()===$.p_stat)&&!(e===x._return&&v.test(this.input.slice(this.lastTokEnd,this.start)))&&(e!==x.colon&&e!==x.braceL||this.curContext()!==$.b_stat)?this.context.push($.f_expr):this.context.push($.f_stat),this.exprAllowed=!1},x.colon.updateContext=function(){"function"===this.curContext().token&&this.context.pop(),this.exprAllowed=!0},x.backQuote.updateContext=function(){this.curContext()===$.q_tmpl?this.context.pop():this.context.push($.q_tmpl),this.exprAllowed=!1},x.star.updateContext=function(e){if(e===x._function){var t=this.context.length-1;this.context[t]===$.f_expr?this.context[t]=$.f_expr_gen:this.context[t]=$.f_gen}this.exprAllowed=!0},x.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==x.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var W=acorn_Parser.prototype;function isLocalVariableAccess(e){return"Identifier"===e.type||"ParenthesizedExpression"===e.type&&isLocalVariableAccess(e.expression)}function isPrivateFieldAccess(e){return"MemberExpression"===e.type&&"PrivateIdentifier"===e.property.type||"ChainExpression"===e.type&&isPrivateFieldAccess(e.expression)||"ParenthesizedExpression"===e.type&&isPrivateFieldAccess(e.expression)}W.checkPropClash=function(e,t,i){if((!(this.options.ecmaVersion>=9)||"SpreadElement"!==e.type)&&(!(this.options.ecmaVersion>=6)||!e.computed&&!e.method&&!e.shorthand)){var s,r=e.key;switch(r.type){case"Identifier":s=r.name;break;case"Literal":s=String(r.value);break;default:return}var n=e.kind;if(this.options.ecmaVersion>=6){"__proto__"===s&&"init"===n&&(t.proto&&(i?i.doubleProto<0&&(i.doubleProto=r.start):this.raiseRecoverable(r.start,"Redefinition of __proto__ property")),t.proto=!0);return}var a=t[s="$"+s];a?("init"===n?this.strict&&a.init||a.get||a.set:a.init||a[n])&&this.raiseRecoverable(r.start,"Redefinition of property"):a=t[s]={init:!1,get:!1,set:!1},a[n]=!0}},W.parseExpression=function(e,t){var i=this.start,s=this.startLoc,r=this.parseMaybeAssign(e,t);if(this.type===x.comma){var n=this.startNodeAt(i,s);for(n.expressions=[r];this.eat(x.comma);)n.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(n,"SequenceExpression")}return r},W.parseMaybeAssign=function(e,t,i){if(this.isContextual("yield"))if(this.inGenerator)return this.parseYield(e);else this.exprAllowed=!1;var s=!1,r=-1,n=-1,a=-1;t?(r=t.parenthesizedAssign,n=t.trailingComma,a=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new acorn_DestructuringErrors,s=!0);var o=this.start,h=this.startLoc;(this.type===x.parenL||this.type===x.name)&&(this.potentialArrowAt=this.start,this.potentialArrowInForAwait="await"===e);var c=this.parseMaybeConditional(e,t);if(i&&(c=i.call(this,c,o,h)),this.type.isAssign){var p=this.startNodeAt(o,h);return p.operator=this.value,this.type===x.eq&&(c=this.toAssignable(c,!1,t)),s||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=c.start&&(t.shorthandAssign=-1),this.type===x.eq?this.checkLValPattern(c):this.checkLValSimple(c),p.left=c,this.next(),p.right=this.parseMaybeAssign(e),a>-1&&(t.doubleProto=a),this.finishNode(p,"AssignmentExpression")}return s&&this.checkExpressionErrors(t,!0),r>-1&&(t.parenthesizedAssign=r),n>-1&&(t.trailingComma=n),c},W.parseMaybeConditional=function(e,t){var i=this.start,s=this.startLoc,r=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return r;if(this.eat(x.question)){var n=this.startNodeAt(i,s);return n.test=r,n.consequent=this.parseMaybeAssign(),this.expect(x.colon),n.alternate=this.parseMaybeAssign(e),this.finishNode(n,"ConditionalExpression")}return r},W.parseExprOps=function(e,t){var i=this.start,s=this.startLoc,r=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||r.start===i&&"ArrowFunctionExpression"===r.type?r:this.parseExprOp(r,i,s,-1,e)},W.parseExprOp=function(e,t,i,s,r){var n=this.type.binop;if(null!=n&&(!r||this.type!==x._in)&&n>s){var a=this.type===x.logicalOR||this.type===x.logicalAND,o=this.type===x.coalesce;o&&(n=x.logicalAND.binop);var h=this.value;this.next();var c=this.start,p=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,r),c,p,n,r),u=this.buildBinary(t,i,e,l,h,a||o);return(a&&this.type===x.coalesce||o&&(this.type===x.logicalOR||this.type===x.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(u,t,i,s,r)}return e},W.buildBinary=function(e,t,i,s,r,n){"PrivateIdentifier"===s.type&&this.raise(s.start,"Private identifier can only be left side of binary expression");var a=this.startNodeAt(e,t);return a.left=i,a.operator=r,a.right=s,this.finishNode(a,n?"LogicalExpression":"BinaryExpression")},W.parseMaybeUnary=function(e,t,i,s){var r,n=this.start,a=this.startLoc;if(this.isContextual("await")&&this.canAwait)r=this.parseAwait(s),t=!0;else if(this.type.prefix){var o=this.startNode(),h=this.type===x.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,h,s),this.checkExpressionErrors(e,!0),h?this.checkLValSimple(o.argument):this.strict&&"delete"===o.operator&&isLocalVariableAccess(o.argument)?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):"delete"===o.operator&&isPrivateFieldAccess(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,r=this.finishNode(o,h?"UpdateExpression":"UnaryExpression")}else if(t||this.type!==x.privateId){if(r=this.parseExprSubscripts(e,s),this.checkExpressionErrors(e))return r;for(;this.type.postfix&&!this.canInsertSemicolon();){var c=this.startNodeAt(n,a);c.operator=this.value,c.prefix=!1,c.argument=r,this.checkLValSimple(r),this.next(),r=this.finishNode(c,"UpdateExpression")}}else(s||0===this.privateNameStack.length)&&this.options.checkPrivateFields&&this.unexpected(),r=this.parsePrivateIdent(),this.type!==x._in&&this.unexpected();return!i&&this.eat(x.starstar)?t?void this.unexpected(this.lastTokStart):this.buildBinary(n,a,r,this.parseMaybeUnary(null,!1,!1,s),"**",!1):r},W.parseExprSubscripts=function(e,t){var i=this.start,s=this.startLoc,r=this.parseExprAtom(e,t);if("ArrowFunctionExpression"===r.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return r;var n=this.parseSubscripts(r,i,s,!1,t);return e&&"MemberExpression"===n.type&&(e.parenthesizedAssign>=n.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=n.start&&(e.parenthesizedBind=-1),e.trailingComma>=n.start&&(e.trailingComma=-1)),n},W.parseSubscripts=function(e,t,i,s,r){for(var n=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.potentialArrowAt===e.start,a=!1;;){var o=this.parseSubscript(e,t,i,s,n,a,r);if(o.optional&&(a=!0),o===e||"ArrowFunctionExpression"===o.type){if(a){var h=this.startNodeAt(t,i);h.expression=o,o=this.finishNode(h,"ChainExpression")}return o}e=o}},W.shouldParseAsyncArrow=function(){return!this.canInsertSemicolon()&&this.eat(x.arrow)},W.parseSubscriptAsyncArrow=function(e,t,i,s){return this.parseArrowExpression(this.startNodeAt(e,t),i,!0,s)},W.parseSubscript=function(e,t,i,s,r,n,a){var o=this.options.ecmaVersion>=11,h=o&&this.eat(x.questionDot);s&&h&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var c=this.eat(x.bracketL);if(c||h&&this.type!==x.parenL&&this.type!==x.backQuote||this.eat(x.dot)){var p=this.startNodeAt(t,i);p.object=e,c?(p.property=this.parseExpression(),this.expect(x.bracketR)):this.type===x.privateId&&"Super"!==e.type?p.property=this.parsePrivateIdent():p.property=this.parseIdent("never"!==this.options.allowReserved),p.computed=!!c,o&&(p.optional=h),e=this.finishNode(p,"MemberExpression")}else if(!s&&this.eat(x.parenL)){var l=new acorn_DestructuringErrors,u=this.yieldPos,d=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var m=this.parseExprList(x.parenR,this.options.ecmaVersion>=8,!1,l);if(r&&!h&&this.shouldParseAsyncArrow())return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=u,this.awaitPos=d,this.awaitIdentPos=f,this.parseSubscriptAsyncArrow(t,i,m,a);this.checkExpressionErrors(l,!0),this.yieldPos=u||this.yieldPos,this.awaitPos=d||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var g=this.startNodeAt(t,i);g.callee=e,g.arguments=m,o&&(g.optional=h),e=this.finishNode(g,"CallExpression")}else if(this.type===x.backQuote){(h||n)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var _=this.startNodeAt(t,i);_.tag=e,_.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(_,"TaggedTemplateExpression")}return e},W.parseExprAtom=function(e,t,i){this.type===x.slash&&this.readRegexp();var s,r=this.potentialArrowAt===this.start;switch(this.type){case x._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),s=this.startNode(),this.next(),this.type!==x.parenL||this.allowDirectSuper||this.raise(s.start,"super() call outside constructor of a subclass"),this.type!==x.dot&&this.type!==x.bracketL&&this.type!==x.parenL&&this.unexpected(),this.finishNode(s,"Super");case x._this:return s=this.startNode(),this.next(),this.finishNode(s,"ThisExpression");case x.name:var n=this.start,a=this.startLoc,o=this.containsEsc,h=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!o&&"async"===h.name&&!this.canInsertSemicolon()&&this.eat(x._function))return this.overrideContext($.f_expr),this.parseFunction(this.startNodeAt(n,a),0,!1,!0,t);if(r&&!this.canInsertSemicolon()){if(this.eat(x.arrow))return this.parseArrowExpression(this.startNodeAt(n,a),[h],!1,t);if(this.options.ecmaVersion>=8&&"async"===h.name&&this.type===x.name&&!o&&(!this.potentialArrowInForAwait||"of"!==this.value||this.containsEsc))return h=this.parseIdent(!1),(this.canInsertSemicolon()||!this.eat(x.arrow))&&this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,a),[h],!0,t)}return h;case x.regexp:var c=this.value;return(s=this.parseLiteral(c.value)).regex={pattern:c.pattern,flags:c.flags},s;case x.num:case x.string:return this.parseLiteral(this.value);case x._null:case x._true:case x._false:return(s=this.startNode()).value=this.type===x._null?null:this.type===x._true,s.raw=this.type.keyword,this.next(),this.finishNode(s,"Literal");case x.parenL:var p=this.start,l=this.parseParenAndDistinguishExpression(r,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(l)&&(e.parenthesizedAssign=p),e.parenthesizedBind<0&&(e.parenthesizedBind=p)),l;case x.bracketL:return s=this.startNode(),this.next(),s.elements=this.parseExprList(x.bracketR,!0,!0,e),this.finishNode(s,"ArrayExpression");case x.braceL:return this.overrideContext($.b_expr),this.parseObj(!1,e);case x._function:return s=this.startNode(),this.next(),this.parseFunction(s,0);case x._class:return this.parseClass(this.startNode(),!1);case x._new:return this.parseNew();case x.backQuote:return this.parseTemplate();case x._import:if(this.options.ecmaVersion>=11)return this.parseExprImport(i);return this.unexpected();default:return this.parseExprAtomDefault()}},W.parseExprAtomDefault=function(){this.unexpected()},W.parseExprImport=function(e){var t=this.startNode();if(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import"),this.next(),this.type===x.parenL&&!e)return this.parseDynamicImport(t);if(this.type===x.dot){var i=this.startNodeAt(t.start,t.loc&&t.loc.start);return i.name="import",t.meta=this.finishNode(i,"Identifier"),this.parseImportMeta(t)}this.unexpected()},W.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),this.options.ecmaVersion>=16)this.eat(x.parenR)?e.options=null:(this.expect(x.comma),this.afterTrailingComma(x.parenR)?e.options=null:(e.options=this.parseMaybeAssign(),!this.eat(x.parenR)&&(this.expect(x.comma),this.afterTrailingComma(x.parenR)||this.unexpected())));else if(!this.eat(x.parenR)){var t=this.start;this.eat(x.comma)&&this.eat(x.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},W.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"===this.options.sourceType||this.options.allowImportExportEverywhere||this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},W.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=null!=t.value?t.value.toString():t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},W.parseParenExpression=function(){this.expect(x.parenL);var e=this.parseExpression();return this.expect(x.parenR),e},W.shouldParseArrow=function(e){return!this.canInsertSemicolon()},W.parseParenAndDistinguishExpression=function(e,t){var i,s=this.start,r=this.startLoc,n=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var a,o=this.start,h=this.startLoc,c=[],p=!0,l=!1,u=new acorn_DestructuringErrors,d=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==x.parenR;){if(p?p=!1:this.expect(x.comma),n&&this.afterTrailingComma(x.parenR,!0)){l=!0;break}if(this.type===x.ellipsis){a=this.start,c.push(this.parseParenItem(this.parseRestBinding())),this.type===x.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element");break}c.push(this.parseMaybeAssign(!1,u,this.parseParenItem))}var m=this.lastTokEnd,g=this.lastTokEndLoc;if(this.expect(x.parenR),e&&this.shouldParseArrow(c)&&this.eat(x.arrow))return this.checkPatternErrors(u,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=d,this.awaitPos=f,this.parseParenArrowList(s,r,c,t);(!c.length||l)&&this.unexpected(this.lastTokStart),a&&this.unexpected(a),this.checkExpressionErrors(u,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=f||this.awaitPos,c.length>1?((i=this.startNodeAt(o,h)).expressions=c,this.finishNodeAt(i,"SequenceExpression",m,g)):i=c[0]}else i=this.parseParenExpression();if(!this.options.preserveParens)return i;var _=this.startNodeAt(s,r);return _.expression=i,this.finishNode(_,"ParenthesizedExpression")},W.parseParenItem=function(e){return e},W.parseParenArrowList=function(e,t,i,s){return this.parseArrowExpression(this.startNodeAt(e,t),i,!1,s)};var G=[];W.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode();if(this.next(),this.options.ecmaVersion>=6&&this.type===x.dot){var t=this.startNodeAt(e.start,e.loc&&e.loc.start);t.name="new",e.meta=this.finishNode(t,"Identifier"),this.next();var i=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var s=this.start,r=this.startLoc;return e.callee=this.parseSubscripts(this.parseExprAtom(null,!1,!0),s,r,!0,!1),this.eat(x.parenL)?e.arguments=this.parseExprList(x.parenR,this.options.ecmaVersion>=8,!1):e.arguments=G,this.finishNode(e,"NewExpression")},W.parseTemplateElement=function(e){var t=e.isTagged,i=this.startNode();return this.type===x.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value.replace(/\r\n?/g,"\n"),cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),i.tail=this.type===x.backQuote,this.finishNode(i,"TemplateElement")},W.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var i=this.startNode();this.next(),i.expressions=[];var s=this.parseTemplateElement({isTagged:t});for(i.quasis=[s];!s.tail;)this.type===x.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(x.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(x.braceR),i.quasis.push(s=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(i,"TemplateLiteral")},W.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===x.name||this.type===x.num||this.type===x.string||this.type===x.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===x.star)&&!v.test(this.input.slice(this.lastTokEnd,this.start))},W.parseObj=function(e,t){var i=this.startNode(),s=!0,r={};for(i.properties=[],this.next();!this.eat(x.braceR);){if(s)s=!1;else if(this.expect(x.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(x.braceR))break;var n=this.parseProperty(e,t);e||this.checkPropClash(n,r,t),i.properties.push(n)}return this.finishNode(i,e?"ObjectPattern":"ObjectExpression")},W.parseProperty=function(e,t){var i,s,r,n,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(x.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===x.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(a.argument=this.parseMaybeAssign(!1,t),this.type===x.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(r=this.start,n=this.startLoc),e||(i=this.eat(x.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(a)?(s=!0,i=this.options.ecmaVersion>=9&&this.eat(x.star),this.parsePropertyName(a)):s=!1,this.parsePropertyValue(a,e,i,s,r,n,t,o),this.finishNode(a,"Property")},W.parseGetterSetter=function(e){var t=e.key.name;this.parsePropertyName(e),e.value=this.parseMethod(!1),e.kind=t;var i=+("get"!==e.kind);if(e.value.params.length!==i){var s=e.value.start;"get"===e.kind?this.raiseRecoverable(s,"getter should have no params"):this.raiseRecoverable(s,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")},W.parsePropertyValue=function(e,t,i,s,r,n,a,o){(i||s)&&this.type===x.colon&&this.unexpected(),this.eat(x.colon)?(e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init"):this.options.ecmaVersion>=6&&this.type===x.parenL?(t&&this.unexpected(),e.method=!0,e.value=this.parseMethod(i,s),e.kind="init"):t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===x.comma||this.type===x.braceR||this.type===x.eq?this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((i||s)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=r),t?e.value=this.parseMaybeDefault(r,n,this.copyNode(e.key)):this.type===x.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(r,n,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.kind="init",e.shorthand=!0):this.unexpected():((i||s)&&this.unexpected(),this.parseGetterSetter(e))},W.parsePropertyName=function(e){if(this.options.ecmaVersion>=6)if(this.eat(x.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(x.bracketR),e.key;else e.computed=!1;return e.key=this.type===x.num||this.type===x.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},W.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},W.parseMethod=function(e,t,i){var s=this.startNode(),r=this.yieldPos,n=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=e),this.options.ecmaVersion>=8&&(s.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|functionFlags(t,s.generator)|128*!!i),this.expect(x.parenL),s.params=this.parseBindingList(x.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0,!1),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=a,this.finishNode(s,"FunctionExpression")},W.parseArrowExpression=function(e,t,i,s){var r=this.yieldPos,n=this.awaitPos,a=this.awaitIdentPos;return this.enterScope(16|functionFlags(i,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,s),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=a,this.finishNode(e,"ArrowFunctionExpression")},W.parseFunctionBody=function(e,t,i,s){var r=t&&this.type!==x.braceL,n=this.strict,a=!1;if(r)e.body=this.parseMaybeAssign(s),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);(!n||o)&&(a=this.strictDirective(this.end))&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var h=this.labels;this.labels=[],a&&(this.strict=!0),this.checkParams(e,!n&&!a&&!t&&!i&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,5),e.body=this.parseBlock(!1,void 0,a&&!n),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=h}this.exitScope()},W.isSimpleParamList=function(e){for(var t=0;t<e.length;t+=1)if("Identifier"!==e[t].type)return!1;return!0},W.checkParams=function(e,t){for(var i=Object.create(null),s=0,r=e.params;s<r.length;s+=1){var n=r[s];this.checkLValInnerPattern(n,1,t?null:i)}},W.parseExprList=function(e,t,i,s){for(var r=[],n=!0;!this.eat(e);){if(n)n=!1;else if(this.expect(x.comma),t&&this.afterTrailingComma(e))break;var a=void 0;i&&this.type===x.comma?a=null:this.type===x.ellipsis?(a=this.parseSpread(s),s&&this.type===x.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):a=this.parseMaybeAssign(!1,s),r.push(a)}return r},W.checkUnreserved=function(e){var t=e.start,i=e.end,s=e.name;this.inGenerator&&"yield"===s&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===s&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),259&this.currentThisScope().flags||"arguments"!==s||this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),this.inClassStaticBlock&&("arguments"===s||"await"===s)&&this.raise(t,"Cannot use "+s+" in class static initialization block"),this.keywords.test(s)&&this.raise(t,"Unexpected keyword '"+s+"'"),(!(this.options.ecmaVersion<6)||-1===this.input.slice(t,i).indexOf("\\"))&&(this.strict?this.reservedWordsStrict:this.reservedWords).test(s)&&(this.inAsync||"await"!==s||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+s+"' is reserved"))},W.parseIdent=function(e){var t=this.parseIdentNode();return this.next(!!e),this.finishNode(t,"Identifier"),!e&&(this.checkUnreserved(t),"await"!==t.name||this.awaitIdentPos||(this.awaitIdentPos=t.start)),t},W.parseIdentNode=function(){var e=this.startNode();return this.type===x.name?e.name=this.value:this.type.keyword?(e.name=this.type.keyword,("class"===e.name||"function"===e.name)&&(this.lastTokEnd!==this.lastTokStart+1||46!==this.input.charCodeAt(this.lastTokStart))&&this.context.pop(),this.type=x.name):this.unexpected(),e},W.parsePrivateIdent=function(){var e=this.startNode();return this.type===x.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),this.options.checkPrivateFields&&(0===this.privateNameStack.length?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e)),e},W.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===x.semi||this.canInsertSemicolon()||this.type!==x.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(x.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},W.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var H=acorn_Parser.prototype;H.raise=function(e,t){var i=getLineInfo(this.input,e);t+=" ("+i.line+":"+i.column+")",this.sourceFile&&(t+=" in "+this.sourceFile);var s=SyntaxError(t);throw s.pos=e,s.loc=i,s.raisedAt=this.pos,s},H.raiseRecoverable=H.raise,H.curPosition=function(){if(this.options.locations)return new acorn_Position(this.curLine,this.pos-this.lineStart)};var z=acorn_Parser.prototype,acorn_Scope=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[]};z.enterScope=function(e){this.scopeStack.push(new acorn_Scope(e))},z.exitScope=function(){this.scopeStack.pop()},z.treatFunctionsAsVarInScope=function(e){return 2&e.flags||!this.inModule&&1&e.flags},z.declareName=function(e,t,i){var s=!1;if(2===t){var r=this.currentScope();s=r.lexical.indexOf(e)>-1||r.functions.indexOf(e)>-1||r.var.indexOf(e)>-1,r.lexical.push(e),this.inModule&&1&r.flags&&delete this.undefinedExports[e]}else if(4===t)this.currentScope().lexical.push(e);else if(3===t){var n=this.currentScope();s=this.treatFunctionsAsVar?n.lexical.indexOf(e)>-1:n.lexical.indexOf(e)>-1||n.var.indexOf(e)>-1,n.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(32&o.flags&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){s=!0;break}if(o.var.push(e),this.inModule&&1&o.flags&&delete this.undefinedExports[e],259&o.flags)break}s&&this.raiseRecoverable(i,"Identifier '"+e+"' has already been declared")},z.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},z.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},z.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(771&t.flags)return t}},z.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(771&t.flags&&!(16&t.flags))return t}};var acorn_Node=function(e,t,i){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new acorn_SourceLocation(e,i)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},K=acorn_Parser.prototype;function finishNodeAt(e,t,i,s){return e.type=t,e.end=i,this.options.locations&&(e.loc.end=s),this.options.ranges&&(e.range[1]=i),e}K.startNode=function(){return new acorn_Node(this,this.start,this.startLoc)},K.startNodeAt=function(e,t){return new acorn_Node(this,e,t)},K.finishNode=function(e,t){return finishNodeAt.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},K.finishNodeAt=function(e,t,i,s){return finishNodeAt.call(this,e,t,i,s)},K.copyNode=function(e){var t=new acorn_Node(this,e.start,this.startLoc);for(var i in e)t[i]=e[i];return t};var Z="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",J=Z+" Extended_Pictographic",Y=J+" EBase EComp EMod EPres ExtPict",Q={9:Z,10:J,11:J,12:Y,13:Y,14:Y},X={9:"",10:"",11:"",12:"",13:"",14:"Basic_Emoji Emoji_Keycap_Sequence RGI_Emoji_Modifier_Sequence RGI_Emoji_Flag_Sequence RGI_Emoji_Tag_Sequence RGI_Emoji_ZWJ_Sequence RGI_Emoji"},ee="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",et="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",ei=et+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",es=ei+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",er=es+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",en=er+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith",ea={9:et,10:ei,11:es,12:er,13:en,14:en+" Gara Garay Gukh Gurung_Khema Hrkt Katakana_Or_Hiragana Kawi Kirat_Rai Krai Nag_Mundari Nagm Ol_Onal Onao Sunu Sunuwar Todhri Todr Tulu_Tigalari Tutg Unknown Zzzz"},eo={};function buildUnicodeData(e){var t=eo[e]={binary:wordsRegexp(Q[e]+" "+ee),binaryOfStrings:wordsRegexp(X[e]),nonBinary:{General_Category:wordsRegexp(ee),Script:wordsRegexp(ea[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var eh=0,ec=[9,10,11,12,13,14];eh<ec.length;eh+=1)buildUnicodeData(ec[eh]);var ep=acorn_Parser.prototype,acorn_BranchID=function(e,t){this.parent=e,this.base=t||this};acorn_BranchID.prototype.separatedFrom=function(e){for(var t=this;t;t=t.parent)for(var i=e;i;i=i.parent)if(t.base===i.base&&t!==i)return!0;return!1},acorn_BranchID.prototype.sibling=function(){return new acorn_BranchID(this.parent,this.base)};var acorn_RegExpValidationState=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":"")+(e.options.ecmaVersion>=15?"v":""),this.unicodeProperties=eo[e.options.ecmaVersion>=14?14:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchV=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=Object.create(null),this.backReferenceNames=[],this.branchID=null};function hasProp(e){for(var t in e)return!0;return!1}function isRegularExpressionModifier(e){return 105===e||109===e||115===e}function isSyntaxCharacter(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function isRegExpIdentifierStart(e){return isIdentifierStart(e,!0)||36===e||95===e}function isRegExpIdentifierPart(e){return isIdentifierChar(e,!0)||36===e||95===e||8204===e||8205===e}function isControlLetter(e){return e>=65&&e<=90||e>=97&&e<=122}function isValidUnicode(e){return e>=0&&e<=1114111}function isCharacterClassEscape(e){return 100===e||68===e||115===e||83===e||119===e||87===e}function isUnicodePropertyNameCharacter(e){return isControlLetter(e)||95===e}function isUnicodePropertyValueCharacter(e){return isUnicodePropertyNameCharacter(e)||isDecimalDigit(e)}function isClassSetReservedDoublePunctuatorCharacter(e){return 33===e||e>=35&&e<=38||e>=42&&e<=44||46===e||e>=58&&e<=64||94===e||96===e||126===e}function isClassSetSyntaxCharacter(e){return 40===e||41===e||45===e||47===e||e>=91&&e<=93||e>=123&&e<=125}function isClassSetReservedPunctuator(e){return 33===e||35===e||37===e||38===e||44===e||45===e||e>=58&&e<=62||64===e||96===e||126===e}function isDecimalDigit(e){return e>=48&&e<=57}function isHexDigit(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function hexToInt(e){return e>=65&&e<=70?10+(e-65):e>=97&&e<=102?10+(e-97):e-48}function isOctalDigit(e){return e>=48&&e<=55}acorn_RegExpValidationState.prototype.reset=function(e,t,i){var s=-1!==i.indexOf("v"),r=-1!==i.indexOf("u");this.start=0|e,this.source=t+"",this.flags=i,s&&this.parser.options.ecmaVersion>=15?(this.switchU=!0,this.switchV=!0,this.switchN=!0):(this.switchU=r&&this.parser.options.ecmaVersion>=6,this.switchV=!1,this.switchN=r&&this.parser.options.ecmaVersion>=9)},acorn_RegExpValidationState.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},acorn_RegExpValidationState.prototype.at=function(e,t){void 0===t&&(t=!1);var i=this.source,s=i.length;if(e>=s)return -1;var r=i.charCodeAt(e);if(!(t||this.switchU)||r<=55295||r>=57344||e+1>=s)return r;var n=i.charCodeAt(e+1);return n>=56320&&n<=57343?(r<<10)+n-0x35fdc00:r},acorn_RegExpValidationState.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var i=this.source,s=i.length;if(e>=s)return s;var r,n=i.charCodeAt(e);return!(t||this.switchU)||n<=55295||n>=57344||e+1>=s||(r=i.charCodeAt(e+1))<56320||r>57343?e+1:e+2},acorn_RegExpValidationState.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},acorn_RegExpValidationState.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},acorn_RegExpValidationState.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},acorn_RegExpValidationState.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},acorn_RegExpValidationState.prototype.eatChars=function(e,t){void 0===t&&(t=!1);for(var i=this.pos,s=0;s<e.length;s+=1){var r=e[s],n=this.at(i,t);if(-1===n||n!==r)return!1;i=this.nextIndex(i,t)}return this.pos=i,!0},ep.validateRegExpFlags=function(e){for(var t=e.validFlags,i=e.flags,s=!1,r=!1,n=0;n<i.length;n++){var a=i.charAt(n);-1===t.indexOf(a)&&this.raise(e.start,"Invalid regular expression flag"),i.indexOf(a,n+1)>-1&&this.raise(e.start,"Duplicate regular expression flag"),"u"===a&&(s=!0),"v"===a&&(r=!0)}this.options.ecmaVersion>=15&&s&&r&&this.raise(e.start,"Invalid regular expression flag")},ep.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&hasProp(e.groupNames)&&(e.switchN=!0,this.regexp_pattern(e))},ep.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames=Object.create(null),e.backReferenceNames.length=0,e.branchID=null,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,i=e.backReferenceNames;t<i.length;t+=1){var s=i[t];e.groupNames[s]||e.raise("Invalid named capture referenced")}},ep.regexp_disjunction=function(e){var t=this.options.ecmaVersion>=16;for(t&&(e.branchID=new acorn_BranchID(e.branchID,null)),this.regexp_alternative(e);e.eat(124);)t&&(e.branchID=e.branchID.sibling()),this.regexp_alternative(e);t&&(e.branchID=e.branchID.parent),this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},ep.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},ep.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):(e.switchU?!!this.regexp_eatAtom(e):!!this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},ep.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!i,!0}return e.pos=t,!1},ep.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},ep.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},ep.regexp_eatBracedQuantifier=function(e,t){var i=e.pos;if(e.eat(123)){var s=0,r=-1;if(this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(r=e.lastIntValue),e.eat(125)))return -1!==r&&r<s&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=i}return!1},ep.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},ep.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},ep.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)){if(this.options.ecmaVersion>=16){var i=this.regexp_eatModifiers(e),s=e.eat(45);if(i||s){for(var r=0;r<i.length;r++){var n=i.charAt(r);i.indexOf(n,r+1)>-1&&e.raise("Duplicate regular expression modifiers")}if(s){var a=this.regexp_eatModifiers(e);i||a||58!==e.current()||e.raise("Invalid regular expression modifiers");for(var o=0;o<a.length;o++){var h=a.charAt(o);(a.indexOf(h,o+1)>-1||i.indexOf(h)>-1)&&e.raise("Duplicate regular expression modifiers")}}}}if(e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}}e.pos=t}return!1},ep.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},ep.regexp_eatModifiers=function(e){for(var t="",i=0;-1!==(i=e.current())&&isRegularExpressionModifier(i);)t+=codePointToString(i),e.advance();return t},ep.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},ep.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},ep.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!isSyntaxCharacter(t)&&(e.lastIntValue=t,e.advance(),!0)},ep.regexp_eatPatternCharacters=function(e){for(var t=e.pos,i=0;-1!==(i=e.current())&&!isSyntaxCharacter(i);)e.advance();return e.pos!==t},ep.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return -1!==t&&36!==t&&(!(t>=40)||!(t<=43))&&46!==t&&63!==t&&91!==t&&94!==t&&124!==t&&(e.advance(),!0)},ep.regexp_groupSpecifier=function(e){if(e.eat(63)){this.regexp_eatGroupName(e)||e.raise("Invalid group");var t=this.options.ecmaVersion>=16,i=e.groupNames[e.lastStringValue];if(i)if(t)for(var s=0;s<i.length;s+=1)i[s].separatedFrom(e.branchID)||e.raise("Duplicate capture group name");else e.raise("Duplicate capture group name");t?(i||(e.groupNames[e.lastStringValue]=[])).push(e.branchID):e.groupNames[e.lastStringValue]=!0}},ep.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},ep.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=codePointToString(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=codePointToString(e.lastIntValue);return!0}return!1},ep.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return(e.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),isRegExpIdentifierStart(s))?(e.lastIntValue=s,!0):(e.pos=t,!1)},ep.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return(e.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),isRegExpIdentifierPart(s))?(e.lastIntValue=s,!0):(e.pos=t,!1)},ep.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},ep.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var i=e.lastIntValue;if(e.switchU)return i>e.maxBackReference&&(e.maxBackReference=i),!0;if(i<=e.numCapturingParens)return!0;e.pos=t}return!1},ep.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},ep.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},ep.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},ep.regexp_eatZero=function(e){return!(48!==e.current()||isDecimalDigit(e.lookahead()))&&(e.lastIntValue=0,e.advance(),!0)},ep.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},ep.regexp_eatControlLetter=function(e){var t=e.current();return!!isControlLetter(t)&&(e.lastIntValue=t%32,e.advance(),!0)},ep.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var i=e.pos,s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var r=e.lastIntValue;if(s&&r>=55296&&r<=56319){var n=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(a>=56320&&a<=57343)return e.lastIntValue=(r-55296)*1024+(a-56320)+65536,!0}e.pos=n,e.lastIntValue=r}return!0}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&isValidUnicode(e.lastIntValue))return!0;s&&e.raise("Invalid unicode escape"),e.pos=i}return!1},ep.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return 99!==t&&(!e.switchN||107!==t)&&(e.lastIntValue=t,e.advance(),!0)},ep.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do e.lastIntValue=10*e.lastIntValue+(t-48),e.advance();while((t=e.current())>=48&&t<=57);return!0}return!1},ep.regexp_eatCharacterClassEscape=function(e){var t,i=e.current();if(isCharacterClassEscape(i))return e.lastIntValue=-1,e.advance(),1;var s=!1;if(e.switchU&&this.options.ecmaVersion>=9&&((s=80===i)||112===i)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&(t=this.regexp_eatUnicodePropertyValueExpression(e))&&e.eat(125))return s&&2===t&&e.raise("Invalid property name"),t;e.raise("Invalid property name")}return 0},ep.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var i=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,i,s),1}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var r=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,r)}return 0},ep.regexp_validateUnicodePropertyNameAndValue=function(e,t,i){w(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(i)||e.raise("Invalid property value")},ep.regexp_validateUnicodePropertyNameOrValue=function(e,t){return e.unicodeProperties.binary.test(t)?1:e.switchV&&e.unicodeProperties.binaryOfStrings.test(t)?2:void e.raise("Invalid property name")},ep.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";isUnicodePropertyNameCharacter(t=e.current());)e.lastStringValue+=codePointToString(t),e.advance();return""!==e.lastStringValue},ep.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";isUnicodePropertyValueCharacter(t=e.current());)e.lastStringValue+=codePointToString(t),e.advance();return""!==e.lastStringValue},ep.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},ep.regexp_eatCharacterClass=function(e){if(e.eat(91)){var t=e.eat(94),i=this.regexp_classContents(e);return e.eat(93)||e.raise("Unterminated character class"),t&&2===i&&e.raise("Negated character class may contain strings"),!0}return!1},ep.regexp_classContents=function(e){return 93===e.current()?1:e.switchV?this.regexp_classSetExpression(e):(this.regexp_nonEmptyClassRanges(e),1)},ep.regexp_nonEmptyClassRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var i=e.lastIntValue;e.switchU&&(-1===t||-1===i)&&e.raise("Invalid character class"),-1!==t&&-1!==i&&t>i&&e.raise("Range out of order in character class")}}},ep.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var i=e.current();(99===i||isOctalDigit(i))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var s=e.current();return 93!==s&&(e.lastIntValue=s,e.advance(),!0)},ep.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},ep.regexp_classSetExpression=function(e){var t,i=1;if(this.regexp_eatClassSetRange(e));else if(t=this.regexp_eatClassSetOperand(e)){2===t&&(i=2);for(var s=e.pos;e.eatChars([38,38]);){if(38!==e.current()&&(t=this.regexp_eatClassSetOperand(e))){2!==t&&(i=1);continue}e.raise("Invalid character in character class")}if(s!==e.pos)return i;for(;e.eatChars([45,45]);)this.regexp_eatClassSetOperand(e)||e.raise("Invalid character in character class");if(s!==e.pos)return i}else e.raise("Invalid character in character class");for(;;)if(!this.regexp_eatClassSetRange(e)){if(!(t=this.regexp_eatClassSetOperand(e)))return i;2===t&&(i=2)}},ep.regexp_eatClassSetRange=function(e){var t=e.pos;if(this.regexp_eatClassSetCharacter(e)){var i=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassSetCharacter(e)){var s=e.lastIntValue;return -1!==i&&-1!==s&&i>s&&e.raise("Range out of order in character class"),!0}e.pos=t}return!1},ep.regexp_eatClassSetOperand=function(e){return this.regexp_eatClassSetCharacter(e)?1:this.regexp_eatClassStringDisjunction(e)||this.regexp_eatNestedClass(e)},ep.regexp_eatNestedClass=function(e){var t=e.pos;if(e.eat(91)){var i=e.eat(94),s=this.regexp_classContents(e);if(e.eat(93))return i&&2===s&&e.raise("Negated character class may contain strings"),s;e.pos=t}if(e.eat(92)){var r=this.regexp_eatCharacterClassEscape(e);if(r)return r;e.pos=t}return null},ep.regexp_eatClassStringDisjunction=function(e){var t=e.pos;if(e.eatChars([92,113])){if(e.eat(123)){var i=this.regexp_classStringDisjunctionContents(e);if(e.eat(125))return i}else e.raise("Invalid escape");e.pos=t}return null},ep.regexp_classStringDisjunctionContents=function(e){for(var t=this.regexp_classString(e);e.eat(124);)2===this.regexp_classString(e)&&(t=2);return t},ep.regexp_classString=function(e){for(var t=0;this.regexp_eatClassSetCharacter(e);)t++;return 1===t?1:2},ep.regexp_eatClassSetCharacter=function(e){var t=e.pos;if(e.eat(92))return!!(this.regexp_eatCharacterEscape(e)||this.regexp_eatClassSetReservedPunctuator(e))||(e.eat(98)?(e.lastIntValue=8,!0):(e.pos=t,!1));var i=e.current();return!(i<0||i===e.lookahead()&&isClassSetReservedDoublePunctuatorCharacter(i)||isClassSetSyntaxCharacter(i))&&(e.advance(),e.lastIntValue=i,!0)},ep.regexp_eatClassSetReservedPunctuator=function(e){var t=e.current();return!!isClassSetReservedPunctuator(t)&&(e.lastIntValue=t,e.advance(),!0)},ep.regexp_eatClassControlLetter=function(e){var t=e.current();return(!!isDecimalDigit(t)||95===t)&&(e.lastIntValue=t%32,e.advance(),!0)},ep.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},ep.regexp_eatDecimalDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;isDecimalDigit(i=e.current());)e.lastIntValue=10*e.lastIntValue+(i-48),e.advance();return e.pos!==t},ep.regexp_eatHexDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;isHexDigit(i=e.current());)e.lastIntValue=16*e.lastIntValue+hexToInt(i),e.advance();return e.pos!==t},ep.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var i=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*i+e.lastIntValue:e.lastIntValue=8*t+i}else e.lastIntValue=t;return!0}return!1},ep.regexp_eatOctalDigit=function(e){var t=e.current();return isOctalDigit(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},ep.regexp_eatFixedHexDigits=function(e,t){var i=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var r=e.current();if(!isHexDigit(r))return e.pos=i,!1;e.lastIntValue=16*e.lastIntValue+hexToInt(r),e.advance()}return!0};var acorn_Token=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new acorn_SourceLocation(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},el=acorn_Parser.prototype;function stringToNumber(e,t){return t?parseInt(e,8):parseFloat(e.replace(/_/g,""))}function stringToBigInt(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}el.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new acorn_Token(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},el.getToken=function(){return this.next(),new acorn_Token(this)},"undefined"!=typeof Symbol&&(el[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===x.eof,value:t}}}}),el.nextToken=function(){var e=this.curContext();return(e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length)?this.finishToken(x.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},el.readToken=function(e){return isIdentifierStart(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},el.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-0x35fdc00},el.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,i=this.input.indexOf("*/",this.pos+=2);if(-1===i&&this.raise(this.pos-2,"Unterminated comment"),this.pos=i+2,this.options.locations)for(var s=void 0,r=t;(s=nextLineBreak(this.input,r,this.pos))>-1;)++this.curLine,r=this.lineStart=s;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,i),t,this.pos,e,this.curPosition())},el.skipLineComment=function(e){for(var t=this.pos,i=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!isNewLine(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,i,this.curPosition())},el.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&b.test(String.fromCharCode(e)))++this.pos;else break e}}},el.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=e,this.value=t,this.updateContext(i)},el.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(x.ellipsis)):(++this.pos,this.finishToken(x.dot))},el.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(x.assign,2):this.finishOp(x.slash,1)},el.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),i=1,s=42===e?x.star:x.modulo;return(this.options.ecmaVersion>=7&&42===e&&42===t&&(++i,s=x.starstar,t=this.input.charCodeAt(this.pos+2)),61===t)?this.finishOp(x.assign,i+1):this.finishOp(s,i)},el.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e)return this.options.ecmaVersion>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(x.assign,3):this.finishOp(124===e?x.logicalOR:x.logicalAND,2);return 61===t?this.finishOp(x.assign,2):this.finishOp(124===e?x.bitwiseOR:x.bitwiseAND,1)},el.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(x.assign,2):this.finishOp(x.bitwiseXOR,1)},el.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45===t&&!this.inModule&&62===this.input.charCodeAt(this.pos+2)&&(0===this.lastTokEnd||v.test(this.input.slice(this.lastTokEnd,this.pos)))?(this.skipLineComment(3),this.skipSpace(),this.nextToken()):this.finishOp(x.incDec,2):61===t?this.finishOp(x.assign,2):this.finishOp(x.plusMin,1)},el.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),i=1;return t===e?(i=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+i))?this.finishOp(x.assign,i+1):this.finishOp(x.bitShift,i):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(i=2),this.finishOp(x.relational,i)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},el.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(x.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(x.arrow)):this.finishOp(61===e?x.eq:x.prefix,1)},el.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(x.questionDot,2)}if(63===t)return e>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(x.assign,3):this.finishOp(x.coalesce,2)}return this.finishOp(x.question,1)},el.readToken_numberSign=function(){var e=this.options.ecmaVersion,t=35;if(e>=13&&(++this.pos,isIdentifierStart(t=this.fullCharCodeAtPos(),!0)||92===t))return this.finishToken(x.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+codePointToString(t)+"'")},el.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(x.parenL);case 41:return++this.pos,this.finishToken(x.parenR);case 59:return++this.pos,this.finishToken(x.semi);case 44:return++this.pos,this.finishToken(x.comma);case 91:return++this.pos,this.finishToken(x.bracketL);case 93:return++this.pos,this.finishToken(x.bracketR);case 123:return++this.pos,this.finishToken(x.braceL);case 125:return++this.pos,this.finishToken(x.braceR);case 58:return++this.pos,this.finishToken(x.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(x.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(x.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+codePointToString(e)+"'")},el.finishOp=function(e,t){var i=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,i)},el.readRegexp=function(){for(var e,t,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(v.test(s)&&this.raise(i,"Unterminated regular expression"),e)e=!1;else{if("["===s)t=!0;else if("]"===s&&t)t=!1;else if("/"===s&&!t)break;e="\\"===s}++this.pos}var r=this.input.slice(i,this.pos);++this.pos;var n=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(n);var o=this.regexpState||(this.regexpState=new acorn_RegExpValidationState(this));o.reset(i,r,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var h=null;try{h=new RegExp(r,a)}catch(e){}return this.finishToken(x.regexp,{pattern:r,flags:a,value:h})},el.readInt=function(e,t,i){for(var s=this.options.ecmaVersion>=12&&void 0===t,r=i&&48===this.input.charCodeAt(this.pos),n=this.pos,a=0,o=0,h=0,c=null==t?1/0:t;h<c;++h,++this.pos){var p=this.input.charCodeAt(this.pos),l=void 0;if(s&&95===p){r&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===h&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=p;continue}if((l=p>=97?p-97+10:p>=65?p-65+10:p>=48&&p<=57?p-48:1/0)>=e)break;o=p,a=a*e+l}return(s&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===n||null!=t&&this.pos-n!==t)?null:a},el.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var i=this.readInt(e);return null==i&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(i=stringToBigInt(this.input.slice(t,this.pos)),++this.pos):isIdentifierStart(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(x.num,i)},el.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var i=this.pos-t>=2&&48===this.input.charCodeAt(t);i&&this.strict&&this.raise(t,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!i&&!e&&this.options.ecmaVersion>=11&&110===s){var r=stringToBigInt(this.input.slice(t,this.pos));return++this.pos,isIdentifierStart(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(x.num,r)}i&&/[89]/.test(this.input.slice(t,this.pos))&&(i=!1),46!==s||i||(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),69!==s&&101!==s||i||((43===(s=this.input.charCodeAt(++this.pos))||45===s)&&++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),isIdentifierStart(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var n=stringToNumber(this.input.slice(t,this.pos),i);return this.finishToken(x.num,n)},el.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},el.readString=function(e){for(var t="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===e)break;92===s?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(!1),i=this.pos):8232===s||8233===s?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(isNewLine(s)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(x.string,t)};var eu={};el.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e===eu)this.readInvalidTemplateToken();else throw e}this.inTemplateElement=!1},el.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw eu;this.raise(e,t)},el.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.pos+1)){if(this.pos===this.start&&(this.type===x.template||this.type===x.invalidTemplate))if(36===i)return this.pos+=2,this.finishToken(x.dollarBraceL);else return++this.pos,this.finishToken(x.backQuote);return e+=this.input.slice(t,this.pos),this.finishToken(x.template,e)}if(92===i)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(isNewLine(i)){switch(e+=this.input.slice(t,this.pos),++this.pos,i){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(i)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},el.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(x.invalidTemplate,this.input.slice(this.start,this.pos));case"\r":"\n"===this.input[this.pos+1]&&++this.pos;case"\n":case"\u2028":case"\u2029":++this.curLine,this.lineStart=this.pos+1}this.raise(this.start,"Unterminated template")},el.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return codePointToString(this.readCodePoint());case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var i=this.pos-1;this.invalidStringToken(i,"Invalid escape sequence in template string")}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(s,8);return r>255&&(r=parseInt(s=s.slice(0,-1),8)),this.pos+=s.length-1,t=this.input.charCodeAt(this.pos),("0"!==s||56===t||57===t)&&(this.strict||e)&&this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(r)}if(isNewLine(t))return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";return String.fromCharCode(t)}},el.readHexChar=function(e){var t=this.pos,i=this.readInt(16,e);return null===i&&this.invalidStringToken(t,"Bad character escape sequence"),i},el.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var r=this.fullCharCodeAtPos();if(isIdentifierChar(r,s))this.pos+=r<=65535?1:2;else if(92===r){this.containsEsc=!0,e+=this.input.slice(i,this.pos);var n=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?isIdentifierStart:isIdentifierChar)(a,s)||this.invalidStringToken(n,"Invalid Unicode escape"),e+=codePointToString(a),i=this.pos}else break;t=!1}return e+this.input.slice(i,this.pos)},el.readWord=function(){var e=this.readWord1(),t=x.name;return this.keywords.test(e)&&(t=_[e]),this.finishToken(t,e)},acorn_Parser.acorn={Parser:acorn_Parser,version:"8.15.0",defaultOptions:P,Position:acorn_Position,SourceLocation:acorn_SourceLocation,getLineInfo:getLineInfo,Node:acorn_Node,TokenType:acorn_TokenType,tokTypes:x,keywordTypes:_,TokContext:acorn_TokContext,tokContexts:$,isIdentifierChar:isIdentifierChar,isIdentifierStart:isIdentifierStart,Token:acorn_Token,isNewLine:isNewLine,lineBreak:v,lineBreakG:y,nonASCIIwhitespace:b};let ed=require("node:module"),ef=require("node:fs"),em=/\/$|\/\?|\/#/,eg=/^\.?\//;function hasTrailingSlash(e="",t){return t?em.test(e):e.endsWith("/")}function withTrailingSlash(e="",t){if(!t)return e.endsWith("/")?e:e+"/";if(hasTrailingSlash(e,!0))return e||"/";let i=e,s="",r=e.indexOf("#");if(-1!==r&&(i=e.slice(0,r),s=e.slice(r),!i))return s;let[n,...a]=i.split("?");return n+"/"+(a.length>0?`?${a.join("?")}`:"")+s}function isNonEmptyURL(e){return e&&"/"!==e}function dist_joinURL(e,...t){let i=e||"";for(let e of t.filter(e=>isNonEmptyURL(e)))if(i){let t=e.replace(eg,"");i=withTrailingSlash(i)+t}else i=e;return i}Symbol.for("ufo:protocolRelative");let e_=/^[A-Za-z]:\//;function pathe_M_eThtNZ_normalizeWindowsPath(e=""){return e?e.replace(/\\/g,"/").replace(e_,e=>e.toUpperCase()):e}let ex=/^[/\\]{2}/,ev=/^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/,ey=/^[A-Za-z]:$/,eb=/.(\.[^./]+|\.)$/,pathe_M_eThtNZ_normalize=function(e){if(0===e.length)return".";let t=(e=pathe_M_eThtNZ_normalizeWindowsPath(e)).match(ex),i=isAbsolute(e),s="/"===e[e.length-1];return 0===(e=normalizeString(e,!i)).length?i?"/":s?"./":".":(s&&(e+="/"),ey.test(e)&&(e+="/"),t)?i?`//${e}`:`//./${e}`:i&&!isAbsolute(e)?`/${e}`:e},pathe_M_eThtNZ_join=function(...e){let t="";for(let i of e)if(i)if(t.length>0){let e="/"===t[t.length-1],s="/"===i[0];e&&s?t+=i.slice(1):t+=e||s?i:`/${i}`}else t+=i;return pathe_M_eThtNZ_normalize(t)};function pathe_M_eThtNZ_cwd(){return"undefined"!=typeof process&&"function"==typeof process.cwd?process.cwd().replace(/\\/g,"/"):"/"}let pathe_M_eThtNZ_resolve=function(...e){e=e.map(e=>pathe_M_eThtNZ_normalizeWindowsPath(e));let t="",i=!1;for(let s=e.length-1;s>=-1&&!i;s--){let r=s>=0?e[s]:pathe_M_eThtNZ_cwd();r&&0!==r.length&&(t=`${r}/${t}`,i=isAbsolute(r))}return(t=normalizeString(t,!i),i&&!isAbsolute(t))?`/${t}`:t.length>0?t:"."};function normalizeString(e,t){let i="",s=0,r=-1,n=0,a=null;for(let o=0;o<=e.length;++o){if(o<e.length)a=e[o];else if("/"===a)break;else a="/";if("/"===a){if(r===o-1||1===n);else if(2===n){if(i.length<2||2!==s||"."!==i[i.length-1]||"."!==i[i.length-2]){if(i.length>2){let e=i.lastIndexOf("/");-1===e?(i="",s=0):s=(i=i.slice(0,e)).length-1-i.lastIndexOf("/"),r=o,n=0;continue}else if(i.length>0){i="",s=0,r=o,n=0;continue}}t&&(i+=i.length>0?"/..":"..",s=2)}else i.length>0?i+=`/${e.slice(r+1,o)}`:i=e.slice(r+1,o),s=o-r-1;r=o,n=0}else"."===a&&-1!==n?++n:n=-1}return i}let isAbsolute=function(e){return ev.test(e)},extname=function(e){if(".."===e)return"";let t=eb.exec(pathe_M_eThtNZ_normalizeWindowsPath(e));return t&&t[1]||""},pathe_M_eThtNZ_dirname=function(e){let t=pathe_M_eThtNZ_normalizeWindowsPath(e).replace(/\/$/,"").split("/").slice(0,-1);return 1===t.length&&ey.test(t[0])&&(t[0]+="/"),t.join("/")||(isAbsolute(e)?"/":".")},basename=function(e,t){let i=pathe_M_eThtNZ_normalizeWindowsPath(e).split("/"),s="";for(let e=i.length-1;e>=0;e--){let t=i[e];if(t){s=t;break}}return t&&s.endsWith(t)?s.slice(0,-t.length):s},eE=require("node:url"),ek=require("node:assert"),eS=require("node:process"),eC=require("node:path"),ew=require("node:v8"),eT=require("node:util"),eI=new Set(ed.builtinModules);function normalizeSlash(e){return e.replace(/\\/g,"/")}let eR={}.hasOwnProperty,eP=/^([A-Z][a-z\d]*)+$/,eA=new Set(["string","function","number","object","Function","Object","boolean","bigint","symbol"]),eN={};function formatList(e,t="and"){return e.length<3?e.join(` ${t} `):`${e.slice(0,-1).join(", ")}, ${t} ${e[e.length-1]}`}let eL=new Map;function createError(e,t,i){return eL.set(e,t),makeNodeErrorWithCode(i,e)}function makeNodeErrorWithCode(e,t){return NodeError;function NodeError(...i){let s=Error.stackTraceLimit;isErrorStackTraceLimitWritable()&&(Error.stackTraceLimit=0);let r=new e;isErrorStackTraceLimitWritable()&&(Error.stackTraceLimit=s);let n=getMessage(t,i,r);return Object.defineProperties(r,{message:{value:n,enumerable:!1,writable:!0,configurable:!0},toString:{value(){return`${this.name} [${t}]: ${this.message}`},enumerable:!1,writable:!0,configurable:!0}}),eD(r),r.code=t,r}}function isErrorStackTraceLimitWritable(){try{if(ew.startupSnapshot.isBuildingSnapshot())return!1}catch{}let e=Object.getOwnPropertyDescriptor(Error,"stackTraceLimit");return void 0===e?Object.isExtensible(Error):eR.call(e,"writable")&&void 0!==e.writable?e.writable:void 0!==e.set}eN.ERR_INVALID_ARG_TYPE=createError("ERR_INVALID_ARG_TYPE",(e,t,i)=>{ek("string"==typeof e,"'name' must be a string"),Array.isArray(t)||(t=[t]);let s="The ";if(e.endsWith(" argument"))s+=`${e} `;else{let t=e.includes(".")?"property":"argument";s+=`"${e}" ${t} `}s+="must be ";let r=[],n=[],a=[];for(let e of t)ek("string"==typeof e,"All expected entries have to be of type string"),eA.has(e)?r.push(e.toLowerCase()):null===eP.exec(e)?(ek("object"!==e,'The value "object" should be written as "Object"'),a.push(e)):n.push(e);if(n.length>0){let e=r.indexOf("object");-1!==e&&(r.slice(e,1),n.push("Object"))}return r.length>0&&(s+=`${r.length>1?"one of type":"of type"} ${formatList(r,"or")}`,(n.length>0||a.length>0)&&(s+=" or ")),n.length>0&&(s+=`an instance of ${formatList(n,"or")}`,a.length>0&&(s+=" or ")),a.length>0&&(a.length>1?s+=`one of ${formatList(a,"or")}`:(a[0].toLowerCase()!==a[0]&&(s+="an "),s+=`${a[0]}`)),s+=`. Received ${determineSpecificType(i)}`},TypeError),eN.ERR_INVALID_MODULE_SPECIFIER=createError("ERR_INVALID_MODULE_SPECIFIER",(e,t,i)=>`Invalid module "${e}" ${t}${i?` imported from ${i}`:""}`,TypeError),eN.ERR_INVALID_PACKAGE_CONFIG=createError("ERR_INVALID_PACKAGE_CONFIG",(e,t,i)=>`Invalid package config ${e}${t?` while importing ${t}`:""}${i?`. ${i}`:""}`,Error),eN.ERR_INVALID_PACKAGE_TARGET=createError("ERR_INVALID_PACKAGE_TARGET",(e,t,i,s=!1,r)=>{let n="string"==typeof i&&!s&&i.length>0&&!i.startsWith("./");return"."===t?(ek(!1===s),`Invalid "exports" main target ${JSON.stringify(i)} defined in the package config ${e}package.json${r?` imported from ${r}`:""}${n?'; targets must start with "./"':""}`):`Invalid "${s?"imports":"exports"}" target ${JSON.stringify(i)} defined for '${t}' in the package config ${e}package.json${r?` imported from ${r}`:""}${n?'; targets must start with "./"':""}`},Error),eN.ERR_MODULE_NOT_FOUND=createError("ERR_MODULE_NOT_FOUND",(e,t,i=!1)=>`Cannot find ${i?"module":"package"} '${e}' imported from ${t}`,Error),eN.ERR_NETWORK_IMPORT_DISALLOWED=createError("ERR_NETWORK_IMPORT_DISALLOWED","import of '%s' by %s is not supported: %s",Error),eN.ERR_PACKAGE_IMPORT_NOT_DEFINED=createError("ERR_PACKAGE_IMPORT_NOT_DEFINED",(e,t,i)=>`Package import specifier "${e}" is not defined${t?` in package ${t}package.json`:""} imported from ${i}`,TypeError),eN.ERR_PACKAGE_PATH_NOT_EXPORTED=createError("ERR_PACKAGE_PATH_NOT_EXPORTED",(e,t,i)=>"."===t?`No "exports" main defined in ${e}package.json${i?` imported from ${i}`:""}`:`Package subpath '${t}' is not defined by "exports" in ${e}package.json${i?` imported from ${i}`:""}`,Error),eN.ERR_UNSUPPORTED_DIR_IMPORT=createError("ERR_UNSUPPORTED_DIR_IMPORT","Directory import '%s' is not supported resolving ES modules imported from %s",Error),eN.ERR_UNSUPPORTED_RESOLVE_REQUEST=createError("ERR_UNSUPPORTED_RESOLVE_REQUEST",'Failed to resolve module specifier "%s" from "%s": Invalid relative URL or base scheme is not hierarchical.',TypeError),eN.ERR_UNKNOWN_FILE_EXTENSION=createError("ERR_UNKNOWN_FILE_EXTENSION",(e,t)=>`Unknown file extension "${e}" for ${t}`,TypeError),eN.ERR_INVALID_ARG_VALUE=createError("ERR_INVALID_ARG_VALUE",(e,t,i="is invalid")=>{let s=(0,eT.inspect)(t);s.length>128&&(s=`${s.slice(0,128)}...`);let r=e.includes(".")?"property":"argument";return`The ${r} '${e}' ${i}. Received ${s}`},TypeError);let eD=(s="__node_internal_"+(n=function(t){let i=isErrorStackTraceLimitWritable();return i&&(e=Error.stackTraceLimit,Error.stackTraceLimit=1/0),Error.captureStackTrace(t),i&&(Error.stackTraceLimit=e),t}).name,Object.defineProperty(n,"name",{value:s}),n);function getMessage(e,t,i){let s=eL.get(e);if(ek(void 0!==s,"expected `message` to be found"),"function"==typeof s)return ek(s.length<=t.length,`Code: ${e}; The provided arguments length (${t.length}) does not match the required ones (${s.length}).`),Reflect.apply(s,i,t);let r=/%[dfijoOs]/g,n=0;for(;null!==r.exec(s);)n++;return(ek(n===t.length,`Code: ${e}; The provided arguments length (${t.length}) does not match the required ones (${n}).`),0===t.length)?s:(t.unshift(s),Reflect.apply(eT.format,null,t))}function determineSpecificType(e){if(null==e)return String(e);if("function"==typeof e&&e.name)return`function ${e.name}`;if("object"==typeof e)return e.constructor&&e.constructor.name?`an instance of ${e.constructor.name}`:`${(0,eT.inspect)(e,{depth:-1})}`;let t=(0,eT.inspect)(e,{colors:!1});return t.length>28&&(t=`${t.slice(0,25)}...`),`type ${typeof e} (${t})`}let eO={}.hasOwnProperty,{ERR_INVALID_PACKAGE_CONFIG:eV}=eN,eU=new Map;function read(e,{base:t,specifier:i}){let s,r=eU.get(e);if(r)return r;try{s=ef.readFileSync(eC.toNamespacedPath(e),"utf8")}catch(e){if("ENOENT"!==e.code)throw e}let n={exists:!1,pjsonPath:e,main:void 0,name:void 0,type:"none",exports:void 0,imports:void 0};if(void 0!==s){let r;try{r=JSON.parse(s)}catch(r){let s=new eV(e,(t?`"${i}" from `:"")+(0,eE.fileURLToPath)(t||i),r.message);throw s.cause=r,s}n.exists=!0,eO.call(r,"name")&&"string"==typeof r.name&&(n.name=r.name),eO.call(r,"main")&&"string"==typeof r.main&&(n.main=r.main),eO.call(r,"exports")&&(n.exports=r.exports),eO.call(r,"imports")&&(n.imports=r.imports),eO.call(r,"type")&&("commonjs"===r.type||"module"===r.type)&&(n.type=r.type)}return eU.set(e,n),n}function getPackageScopeConfig(e){let t=new URL("package.json",e);for(;!t.pathname.endsWith("node_modules/package.json");){let i=read((0,eE.fileURLToPath)(t),{specifier:e});if(i.exists)return i;let s=t;if((t=new URL("../package.json",t)).pathname===s.pathname)break}return{pjsonPath:(0,eE.fileURLToPath)(t),exists:!1,type:"none"}}let{ERR_UNKNOWN_FILE_EXTENSION:eM}=eN,ej={}.hasOwnProperty,eB={__proto__:null,".cjs":"commonjs",".js":"module",".json":"json",".mjs":"module"};function mimeToFormat(e){return e&&/\s*(text|application)\/javascript\s*(;\s*charset=utf-?8\s*)?/i.test(e)?"module":"application/json"===e?"json":null}let eF={__proto__:null,"data:":getDataProtocolModuleFormat,"file:":getFileProtocolModuleFormat,"http:":getHttpProtocolModuleFormat,"https:":getHttpProtocolModuleFormat,"node:":()=>"builtin"};function getDataProtocolModuleFormat(e){let{1:t}=/^([^/]+\/[^;,]+)[^,]*?(;base64)?,/.exec(e.pathname)||[null,null,null];return mimeToFormat(t)}function dist_extname(e){let t=e.pathname,i=t.length;for(;i--;){let e=t.codePointAt(i);if(47===e)break;if(46===e)return 47===t.codePointAt(i-1)?"":t.slice(i)}return""}function getFileProtocolModuleFormat(e,t,i){let s=dist_extname(e);if(".js"===s){let t=getPackageScopeConfig(e).type;return"none"!==t?t:"commonjs"}if(""===s){let t=getPackageScopeConfig(e).type;return"none"===t||"commonjs"===t?"commonjs":"module"}let r=eB[s];if(r)return r;if(!i)throw new eM(s,(0,eE.fileURLToPath)(e))}function getHttpProtocolModuleFormat(){}function defaultGetFormatWithoutErrors(e,t){let i=e.protocol;return ej.call(eF,i)&&eF[i](e,t,!0)||null}let e$=RegExp.prototype[Symbol.replace],{ERR_INVALID_MODULE_SPECIFIER:eq,ERR_INVALID_PACKAGE_CONFIG:eW,ERR_INVALID_PACKAGE_TARGET:eG,ERR_MODULE_NOT_FOUND:eH,ERR_PACKAGE_IMPORT_NOT_DEFINED:ez,ERR_PACKAGE_PATH_NOT_EXPORTED:eK,ERR_UNSUPPORTED_DIR_IMPORT:eZ,ERR_UNSUPPORTED_RESOLVE_REQUEST:eJ}=eN,eY={}.hasOwnProperty,eQ=/(^|\\|\/)((\.|%2e)(\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))?(\\|\/|$)/i,eX=/(^|\\|\/)((\.|%2e)(\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))(\\|\/|$)/i,e1=/^\.|%|\\/,e0=/\*/g,e2=/%2f|%5c/i,e3=new Set,e4=/[/\\]{2}/;function emitInvalidSegmentDeprecation(e,t,i,s,r,n,a){if(eS.noDeprecation)return;let o=(0,eE.fileURLToPath)(s),h=null!==e4.exec(a?e:t);eS.emitWarning(`Use of deprecated ${h?"double slash":"leading or trailing slash matching"} resolving "${e}" for module request "${t}" ${t===i?"":`matched to "${i}" `}in the "${r?"imports":"exports"}" field module resolution of the package at ${o}${n?` imported from ${(0,eE.fileURLToPath)(n)}`:""}.`,"DeprecationWarning","DEP0166")}function emitLegacyIndexDeprecation(e,t,i,s){if(eS.noDeprecation||"module"!==defaultGetFormatWithoutErrors(e,{parentURL:i.href}))return;let r=(0,eE.fileURLToPath)(e.href),n=(0,eE.fileURLToPath)(new eE.URL(".",t)),a=(0,eE.fileURLToPath)(i);s?eC.resolve(n,s)!==r&&eS.emitWarning(`Package ${n} has a "main" field set to "${s}", excluding the full filename and extension to the resolved file at "${r.slice(n.length)}", imported from ${a}.
 Automatic extension resolution of the "main" field is deprecated for ES modules.`,"DeprecationWarning","DEP0151"):eS.emitWarning(`No "main" or "exports" field defined in the package.json for ${n} resolving the main entry point "${r.slice(n.length)}", imported from ${a}.
Default "index" lookups for the main are deprecated for ES modules.`,"DeprecationWarning","DEP0151")}function tryStatSync(e){try{return(0,ef.statSync)(e)}catch{}}function fileExists(e){let t=(0,ef.statSync)(e,{throwIfNoEntry:!1}),i=t?t.isFile():void 0;return null!=i&&i}function legacyMainResolve(e,t,i){let s;if(void 0!==t.main){if(fileExists(s=new eE.URL(t.main,e)))return s;let r=[`./${t.main}.js`,`./${t.main}.json`,`./${t.main}.node`,`./${t.main}/index.js`,`./${t.main}/index.json`,`./${t.main}/index.node`],n=-1;for(;++n<r.length&&!fileExists(s=new eE.URL(r[n],e));)s=void 0;if(s)return emitLegacyIndexDeprecation(s,e,i,t.main),s}let r=["./index.js","./index.json","./index.node"],n=-1;for(;++n<r.length&&!fileExists(s=new eE.URL(r[n],e));)s=void 0;if(s)return emitLegacyIndexDeprecation(s,e,i,t.main),s;throw new eH((0,eE.fileURLToPath)(new eE.URL(".",e)),(0,eE.fileURLToPath)(i))}function finalizeResolution(e,t,i){let s;if(null!==e2.exec(e.pathname))throw new eq(e.pathname,'must not include encoded "/" or "\\" characters',(0,eE.fileURLToPath)(t));try{s=(0,eE.fileURLToPath)(e)}catch(i){throw Object.defineProperty(i,"input",{value:String(e)}),Object.defineProperty(i,"module",{value:String(t)}),i}let r=tryStatSync(s.endsWith("/")?s.slice(-1):s);if(r&&r.isDirectory()){let i=new eZ(s,(0,eE.fileURLToPath)(t));throw i.url=String(e),i}if(!r||!r.isFile()){let i=new eH(s||e.pathname,t&&(0,eE.fileURLToPath)(t),!0);throw i.url=String(e),i}{let t=(0,ef.realpathSync)(s),{search:i,hash:r}=e;(e=(0,eE.pathToFileURL)(t+(s.endsWith(eC.sep)?"/":""))).search=i,e.hash=r}return e}function importNotDefined(e,t,i){return new ez(e,t&&(0,eE.fileURLToPath)(new eE.URL(".",t)),(0,eE.fileURLToPath)(i))}function exportsNotFound(e,t,i){return new eK((0,eE.fileURLToPath)(new eE.URL(".",t)),e,i&&(0,eE.fileURLToPath)(i))}function throwInvalidSubpath(e,t,i,s,r){throw new eq(e,`request is not a valid match in pattern "${t}" for the "${s?"imports":"exports"}" resolution of ${(0,eE.fileURLToPath)(i)}`,r&&(0,eE.fileURLToPath)(r))}function invalidPackageTarget(e,t,i,s,r){return t="object"==typeof t&&null!==t?JSON.stringify(t,null,""):`${t}`,new eG((0,eE.fileURLToPath)(new eE.URL(".",i)),e,t,s,r&&(0,eE.fileURLToPath)(r))}function resolvePackageTargetString(e,t,i,s,r,n,a,o,h){if(""!==t&&!n&&"/"!==e[e.length-1])throw invalidPackageTarget(i,e,s,a,r);if(!e.startsWith("./")){if(a&&!e.startsWith("../")&&!e.startsWith("/")){let i=!1;try{new eE.URL(e),i=!0}catch{}if(!i)return packageResolve(n?e$.call(e0,e,()=>t):e+t,s,h)}throw invalidPackageTarget(i,e,s,a,r)}if(null!==eQ.exec(e.slice(2)))if(null===eX.exec(e.slice(2))){if(!o){let o=n?i.replace("*",()=>t):i+t;emitInvalidSegmentDeprecation(n?e$.call(e0,e,()=>t):e,o,i,s,a,r,!0)}}else throw invalidPackageTarget(i,e,s,a,r);let c=new eE.URL(e,s),p=c.pathname,l=new eE.URL(".",s).pathname;if(!p.startsWith(l))throw invalidPackageTarget(i,e,s,a,r);if(""===t)return c;if(null!==eQ.exec(t)){let h=n?i.replace("*",()=>t):i+t;null===eX.exec(t)?o||emitInvalidSegmentDeprecation(n?e$.call(e0,e,()=>t):e,h,i,s,a,r,!1):throwInvalidSubpath(h,i,s,a,r)}return n?new eE.URL(e$.call(e0,c.href,()=>t)):new eE.URL(t,c)}function isArrayIndex(e){let t=Number(e);return`${t}`===e&&t>=0&&t<0xffffffff}function resolvePackageTarget(e,t,i,s,r,n,a,o,h){if("string"==typeof t)return resolvePackageTargetString(t,i,s,e,r,n,a,o,h);if(Array.isArray(t)){let c;if(0===t.length)return null;let p=-1;for(;++p<t.length;){let l,u=t[p];try{l=resolvePackageTarget(e,u,i,s,r,n,a,o,h)}catch(e){if(c=e,"ERR_INVALID_PACKAGE_TARGET"===e.code)continue;throw e}if(void 0!==l){if(null===l){c=null;continue}return l}}if(null==c)return null;throw c}if("object"==typeof t&&null!==t){let c=Object.getOwnPropertyNames(t),p=-1;for(;++p<c.length;)if(isArrayIndex(c[p]))throw new eW((0,eE.fileURLToPath)(e),r,'"exports" cannot contain numeric property keys.');for(p=-1;++p<c.length;){let l=c[p];if("default"===l||h&&h.has(l)){let c=resolvePackageTarget(e,t[l],i,s,r,n,a,o,h);if(void 0===c)continue;return c}}return null}if(null===t)return null;throw invalidPackageTarget(s,t,e,a,r)}function isConditionalExportsMainSugar(e,t,i){if("string"==typeof e||Array.isArray(e))return!0;if("object"!=typeof e||null===e)return!1;let s=Object.getOwnPropertyNames(e),r=!1,n=0,a=-1;for(;++a<s.length;){let e=s[a],o=""===e||"."!==e[0];if(0==n++)r=o;else if(r!==o)throw new eW((0,eE.fileURLToPath)(t),i,"\"exports\" cannot contain some keys starting with '.' and some not. The exports object must either be an object of package subpath keys or an object of main entry condition name keys only.")}return r}function emitTrailingSlashPatternDeprecation(e,t,i){if(eS.noDeprecation)return;let s=(0,eE.fileURLToPath)(t);e3.has(s+"|"+e)||(e3.add(s+"|"+e),eS.emitWarning(`Use of deprecated trailing slash pattern mapping "${e}" in the "exports" field module resolution of the package at ${s}${i?` imported from ${(0,eE.fileURLToPath)(i)}`:""}. Mapping specifiers ending in "/" is no longer supported.`,"DeprecationWarning","DEP0155"))}function packageExportsResolve(e,t,i,s,r){let n=i.exports;if(isConditionalExportsMainSugar(n,e,s)&&(n={".":n}),eY.call(n,t)&&!t.includes("*")&&!t.endsWith("/")){let i=resolvePackageTarget(e,n[t],"",t,s,!1,!1,!1,r);if(null==i)throw exportsNotFound(t,e,s);return i}let a="",o="",h=Object.getOwnPropertyNames(n),c=-1;for(;++c<h.length;){let i=h[c],r=i.indexOf("*");if(-1!==r&&t.startsWith(i.slice(0,r))){t.endsWith("/")&&emitTrailingSlashPatternDeprecation(t,e,s);let n=i.slice(r+1);t.length>=i.length&&t.endsWith(n)&&1===patternKeyCompare(a,i)&&i.lastIndexOf("*")===r&&(a=i,o=t.slice(r,t.length-n.length))}}if(a){let i=resolvePackageTarget(e,n[a],o,a,s,!0,!1,t.endsWith("/"),r);if(null==i)throw exportsNotFound(t,e,s);return i}throw exportsNotFound(t,e,s)}function patternKeyCompare(e,t){let i=e.indexOf("*"),s=t.indexOf("*"),r=-1===i?e.length:i+1,n=-1===s?t.length:s+1;return r>n?-1:n>r||-1===i?1:-1===s||e.length>t.length?-1:+(t.length>e.length)}function packageImportsResolve(e,t,i){let s;if("#"===e||e.startsWith("#/")||e.endsWith("/"))throw new eq(e,"is not a valid internal imports specifier name",(0,eE.fileURLToPath)(t));let r=getPackageScopeConfig(t);if(r.exists){s=(0,eE.pathToFileURL)(r.pjsonPath);let n=r.imports;if(n)if(eY.call(n,e)&&!e.includes("*")){let r=resolvePackageTarget(s,n[e],"",e,t,!1,!0,!1,i);if(null!=r)return r}else{let r="",a="",o=Object.getOwnPropertyNames(n),h=-1;for(;++h<o.length;){let t=o[h],i=t.indexOf("*");if(-1!==i&&e.startsWith(t.slice(0,-1))){let s=t.slice(i+1);e.length>=t.length&&e.endsWith(s)&&1===patternKeyCompare(r,t)&&t.lastIndexOf("*")===i&&(r=t,a=e.slice(i,e.length-s.length))}}if(r){let e=resolvePackageTarget(s,n[r],a,r,t,!0,!0,!1,i);if(null!=e)return e}}}throw importNotDefined(e,s,t)}function parsePackageName(e,t){let i=e.indexOf("/"),s=!0,r=!1;"@"===e[0]&&(r=!0,-1===i||0===e.length?s=!1:i=e.indexOf("/",i+1));let n=-1===i?e:e.slice(0,i);if(null!==e1.exec(n)&&(s=!1),!s)throw new eq(e,"is not a valid package name",(0,eE.fileURLToPath)(t));return{packageName:n,packageSubpath:"."+(-1===i?"":e.slice(i)),isScoped:r}}function packageResolve(e,t,i){let s;if(ed.builtinModules.includes(e))return new eE.URL("node:"+e);let{packageName:r,packageSubpath:n,isScoped:a}=parsePackageName(e,t),o=getPackageScopeConfig(t);if(o.exists){let e=(0,eE.pathToFileURL)(o.pjsonPath);if(o.name===r&&void 0!==o.exports&&null!==o.exports)return packageExportsResolve(e,n,o,t,i)}let h=new eE.URL("./node_modules/"+r+"/package.json",t),c=(0,eE.fileURLToPath)(h);do{let o=tryStatSync(c.slice(0,-13));if(!o||!o.isDirectory()){s=c,h=new eE.URL((a?"../../../../node_modules/":"../../../node_modules/")+r+"/package.json",h),c=(0,eE.fileURLToPath)(h);continue}let p=read(c,{base:t,specifier:e});if(void 0!==p.exports&&null!==p.exports)return packageExportsResolve(h,n,p,t,i);if("."===n)return legacyMainResolve(h,p,t);return new eE.URL(n,h)}while(c.length!==s.length);throw new eH(r,(0,eE.fileURLToPath)(t),!1)}function isRelativeSpecifier(e){return"."===e[0]&&(1===e.length||"/"===e[1]||"."===e[1]&&(2===e.length||"/"===e[2]))||!1}function shouldBeTreatedAsRelativeOrAbsolutePath(e){return""!==e&&("/"===e[0]||isRelativeSpecifier(e))}function moduleResolve(e,t,i,s){let r,n=t.protocol;if(shouldBeTreatedAsRelativeOrAbsolutePath(e))try{r=new eE.URL(e,t)}catch(s){let i=new eJ(e,t);throw i.cause=s,i}else if("file:"===n&&"#"===e[0])r=packageImportsResolve(e,t,i);else try{r=new eE.URL(e)}catch(s){if(("data:"===n||"http:"===n||"https:"===n)&&!ed.builtinModules.includes(e)){let i=new eJ(e,t);throw i.cause=s,i}r=packageResolve(e,t,i)}return(ek(void 0!==r,"expected to be defined"),"file:"!==r.protocol)?r:finalizeResolution(r,t)}function fileURLToPath(e){return"string"!=typeof e||e.startsWith("file://")?normalizeSlash((0,eE.fileURLToPath)(e)):normalizeSlash(e)}function pathToFileURL(e){return(0,eE.pathToFileURL)(fileURLToPath(e)).toString()}function normalizeid(e){return("string"!=typeof e&&(e=e.toString()),/(?:node|data|http|https|file):/.test(e))?e:eI.has(e)?"node:"+e:"file://"+encodeURI(normalizeSlash(e))}let e6=new Set(["node","import"]),e5=[".mjs",".cjs",".js",".json"],e9=new Set(["ERR_MODULE_NOT_FOUND","ERR_UNSUPPORTED_DIR_IMPORT","MODULE_NOT_FOUND","ERR_PACKAGE_PATH_NOT_EXPORTED"]);function _tryModuleResolve(e,t,i){try{return moduleResolve(e,t,i)}catch(e){if(!e9.has(e?.code))throw e}}function _resolve(e,t={}){let i;if("string"!=typeof e)if(e instanceof URL)e=fileURLToPath(e);else throw TypeError("input must be a `string` or `URL`");if(/(?:node|data|http|https):/.test(e))return e;if(eI.has(e))return"node:"+e;if(e.startsWith("file://")&&(e=fileURLToPath(e)),isAbsolute(e))try{if((0,ef.statSync)(e).isFile())return pathToFileURL(e)}catch(e){if(e?.code!=="ENOENT")throw e}let s=t.conditions?new Set(t.conditions):e6,r=(Array.isArray(t.url)?t.url:[t.url]).filter(Boolean).map(e=>new URL(normalizeid(e.toString())));0===r.length&&r.push(new URL(pathToFileURL(process.cwd())));let n=[...r];for(let e of r)"file:"===e.protocol&&n.push(new URL("./",e),new URL(dist_joinURL(e.pathname,"_index.js"),e),new URL("node_modules",e));for(let r of n){if(i=_tryModuleResolve(e,r,s))break;for(let n of["","/index"]){for(let a of t.extensions||e5)if(i=_tryModuleResolve(dist_joinURL(e,n)+a,r,s))break;if(i)break}if(i)break}if(!i){let t=Error(`Cannot find module ${e} imported from ${n.join(", ")}`);throw t.code="ERR_MODULE_NOT_FOUND",t}return pathToFileURL(i)}function resolveSync(e,t){return _resolve(e,t)}function resolvePathSync(e,t){return fileURLToPath(resolveSync(e,t))}let e8=/(?:[\s;]|^)(?:import[\s\w*,{}]*from|import\s*["'*{]|export\b\s*(?:[*{]|default|class|type|function|const|var|let|async function)|import\.meta\b)/m,e7=/\/\*.+?\*\/|\/\/.*(?=[nr])/g;function hasESMSyntax(e,t={}){return t.stripComments&&(e=e.replace(e7,"")),e8.test(e)}function escapeStringRegexp(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}let te=new Set(["/","\\",void 0]),tt=Symbol.for("pathe:normalizedAlias"),ti=/[/\\]/;function normalizeAliases(e){if(e[tt])return e;let t=Object.fromEntries(Object.entries(e).sort(([e],[t])=>_compareAliases(e,t)));for(let e in t)for(let i in t)!(i===e||e.startsWith(i))&&t[e]?.startsWith(i)&&te.has(t[e][i.length])&&(t[e]=t[i]+t[e].slice(i.length));return Object.defineProperty(t,tt,{value:!0,enumerable:!1}),t}function resolveAlias(e,t){let i=pathe_M_eThtNZ_normalizeWindowsPath(e);for(let[e,s]of Object.entries(t=normalizeAliases(t))){if(!i.startsWith(e))continue;let t=utils_hasTrailingSlash(e)?e.slice(0,-1):e;if(utils_hasTrailingSlash(i[t.length]))return pathe_M_eThtNZ_join(s,i.slice(e.length))}return i}function utils_filename(e){let t=e.split(ti).pop();if(!t)return;let i=t.lastIndexOf(".");return i<=0?t:t.slice(0,i)}function _compareAliases(e,t){return t.split("/").length-e.split("/").length}function utils_hasTrailingSlash(e="/"){let t=e[e.length-1];return"/"===t||"\\"===t}let ts=require("node:crypto");var tr=__webpack_require__.n(ts);let tn=Object.create(null),dist_i=e=>globalThis.process?.env||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?tn:globalThis),ta=new Proxy(tn,{get:(e,t)=>dist_i()[t]??tn[t],has:(e,t)=>t in dist_i()||t in tn,set:(e,t,i)=>(dist_i(!0)[t]=i,!0),deleteProperty(e,t){if(!t)return!1;let i=dist_i(!0);return delete i[t],!0},ownKeys:()=>Object.keys(dist_i(!0))}),to="u">typeof process&&process.env&&process.env.NODE_ENV||"",th=[["APPVEYOR"],["AWS_AMPLIFY","AWS_APP_ID",{ci:!0}],["AZURE_PIPELINES","SYSTEM_TEAMFOUNDATIONCOLLECTIONURI"],["AZURE_STATIC","INPUT_AZURE_STATIC_WEB_APPS_API_TOKEN"],["APPCIRCLE","AC_APPCIRCLE"],["BAMBOO","bamboo_planKey"],["BITBUCKET","BITBUCKET_COMMIT"],["BITRISE","BITRISE_IO"],["BUDDY","BUDDY_WORKSPACE_ID"],["BUILDKITE"],["CIRCLE","CIRCLECI"],["CIRRUS","CIRRUS_CI"],["CLOUDFLARE_PAGES","CF_PAGES",{ci:!0}],["CLOUDFLARE_WORKERS","WORKERS_CI",{ci:!0}],["CODEBUILD","CODEBUILD_BUILD_ARN"],["CODEFRESH","CF_BUILD_ID"],["DRONE"],["DRONE","DRONE_BUILD_EVENT"],["DSARI"],["GITHUB_ACTIONS"],["GITLAB","GITLAB_CI"],["GITLAB","CI_MERGE_REQUEST_ID"],["GOCD","GO_PIPELINE_LABEL"],["LAYERCI"],["HUDSON","HUDSON_URL"],["JENKINS","JENKINS_URL"],["MAGNUM"],["NETLIFY"],["NETLIFY","NETLIFY_LOCAL",{ci:!1}],["NEVERCODE"],["RENDER"],["SAIL","SAILCI"],["SEMAPHORE"],["SCREWDRIVER"],["SHIPPABLE"],["SOLANO","TDDIUM"],["STRIDER"],["TEAMCITY","TEAMCITY_VERSION"],["TRAVIS"],["VERCEL","NOW_BUILDER"],["VERCEL","VERCEL",{ci:!1}],["VERCEL","VERCEL_ENV",{ci:!1}],["APPCENTER","APPCENTER_BUILD_ID"],["CODESANDBOX","CODESANDBOX_SSE",{ci:!1}],["CODESANDBOX","CODESANDBOX_HOST",{ci:!1}],["STACKBLITZ"],["STORMKIT"],["CLEAVR"],["ZEABUR"],["CODESPHERE","CODESPHERE_APP_ID",{ci:!0}],["RAILWAY","RAILWAY_PROJECT_ID"],["RAILWAY","RAILWAY_SERVICE_ID"],["DENO-DEPLOY","DENO_DEPLOYMENT_ID"],["FIREBASE_APP_HOSTING","FIREBASE_APP_HOSTING",{ci:!0}]],tc=function(){if(globalThis.process?.env)for(let e of th){let t=e[1]||e[0];if(globalThis.process?.env[t])return{name:e[0].toLowerCase(),...e[2]}}return globalThis.process?.env?.SHELL==="/bin/jsh"&&globalThis.process?.versions?.webcontainer?{name:"stackblitz",ci:!1}:{name:"",ci:!1}}();function std_env_dist_n(e){return!!e&&"false"!==e}tc.name;let tp=globalThis.process?.platform||"",tl=(std_env_dist_n(ta.CI)||tc.ci,std_env_dist_n(globalThis.process?.stdout&&globalThis.process?.stdout.isTTY)),tu=(std_env_dist_n(ta.DEBUG),"test"===to||std_env_dist_n(ta.TEST),std_env_dist_n(ta.MINIMAL),/^win/i.test(tp)),td=(/^linux/i.test(tp),/^darwin/i.test(tp),!std_env_dist_n(ta.NO_COLOR)&&(std_env_dist_n(ta.FORCE_COLOR)||(tl||tu)&&ta.TERM),(globalThis.process?.versions?.node||"").replace(/^v/,"")||null),tf=(td?.split(".")[0],globalThis.process||Object.create(null)),tm={versions:{}},tg=(new Proxy(tf,{get:(e,t)=>"env"===t?ta:t in e?e[t]:t in tm?tm[t]:void 0}),globalThis.process?.release?.name==="node"),t_=!!globalThis.Bun||!!globalThis.process?.versions?.bun,tx=!!globalThis.Deno,tv=!!globalThis.fastly,ty=[[!!globalThis.Netlify,"netlify"],[!!globalThis.EdgeRuntime,"edge-light"],[globalThis.navigator?.userAgent==="Cloudflare-Workers","workerd"],[tv,"fastly"],[tx,"deno"],[t_,"bun"],[tg,"node"]],tb=function(){let e=ty.find(e=>e[0]);if(e)return{name:e[1]}}();tb?.name;let tE=require("node:tty"),tk=tE?.WriteStream?.prototype?.hasColors?.()??!1,base_format=(e,t)=>{if(!tk)return e=>e;let i=`\u001B[${e}m`,s=`\u001B[${t}m`;return e=>{let r=e+"",n=r.indexOf(s);if(-1===n)return i+r+s;let a=i,o=0,h=(22===t?s:"")+i;for(;-1!==n;)a+=r.slice(o,n)+h,o=n+s.length,n=r.indexOf(s,o);return a+(r.slice(o)+s)}};base_format(0,0),base_format(1,22),base_format(2,22),base_format(3,23),base_format(4,24),base_format(53,55),base_format(7,27),base_format(8,28),base_format(9,29),base_format(30,39);let tS=base_format(31,39),tC=base_format(32,39),tw=base_format(33,39),tT=base_format(34,39);base_format(35,39);let tI=base_format(36,39);base_format(37,39);let tR=base_format(90,39);function isDir(e){if("string"!=typeof e||e.startsWith("file://"))return!1;try{return(0,ef.lstatSync)(e).isDirectory()}catch{return!1}}function isWritable(e){try{return(0,ef.accessSync)(e,ef.constants.W_OK),!0}catch{return!1}}function utils_hash(e,t=8){return(isFipsMode()?tr().createHash("sha256"):tr().createHash("md5")).update(e).digest("hex").slice(0,t)}function readNearestPackageJSON(e){for(;e&&"."!==e&&"/"!==e;){e=pathe_M_eThtNZ_join(e,"..");try{let t=(0,ef.readFileSync)(pathe_M_eThtNZ_join(e,"package.json"),"utf8");try{return JSON.parse(t)}catch{}break}catch{}}}function wrapModule(e,t){return`(${t?.async?"async ":""}function (exports, require, module, __filename, __dirname, jitiImport, jitiESMResolve) { ${e}
});`}base_format(40,49),base_format(41,49),base_format(42,49),base_format(43,49),base_format(44,49),base_format(45,49),base_format(46,49),base_format(47,49),base_format(100,49),base_format(91,39),base_format(92,39),base_format(93,39),base_format(94,39),base_format(95,39),base_format(96,39),base_format(97,39),base_format(101,49),base_format(102,49),base_format(103,49),base_format(104,49),base_format(105,49),base_format(106,49),base_format(107,49);let tP={true:tC("true"),false:tw("false"),"[rebuild]":tw("[rebuild]"),"[esm]":tT("[esm]"),"[cjs]":tC("[cjs]"),"[import]":tT("[import]"),"[require]":tC("[require]"),"[native]":tI("[native]"),"[transpile]":tw("[transpile]"),"[fallback]":tS("[fallback]"),"[unknown]":tS("[unknown]"),"[hit]":tC("[hit]"),"[miss]":tw("[miss]"),"[json]":tC("[json]"),"[data]":tC("[data]")};function debug(e,...t){if(!e.opts.debug)return;let i=process.cwd();console.log(tR(["[jiti]",...t.map(e=>e in tP?tP[e]:"string"!=typeof e?JSON.stringify(e):e.replace(i,"."))].join(" ")))}function jitiInteropDefault(e,t){return e.opts.interopDefault?utils_interopDefault(t):t}function utils_interopDefault(e){let t=typeof e;if(null===e||"object"!==t&&"function"!==t)return e;let i=e.default,s=typeof i,r=null==i,n="object"===s||"function"===s;return r&&e instanceof Promise?e:new Proxy(e,{get(t,s,a){if("__esModule"===s)return!0;if("default"===s)return r?e:"function"==typeof i?.default&&e.__esModule?i.default:i;if(Reflect.has(t,s))return Reflect.get(t,s,a);if(n){let e=Reflect.get(i,s,a);return"function"==typeof e&&(e=e.bind(i)),e}},apply:(e,t,r)=>"function"==typeof e?Reflect.apply(e,t,r):"function"===s?Reflect.apply(i,t,r):void 0})}function normalizeWindowsImportId(e){return tu&&isAbsolute(e)?pathToFileURL(e):e}function isFipsMode(){if(void 0!==t)return t;try{return t=!!tr().getFips?.()}catch{return t=!1}}function resolveJitiOptions(e){let t={fsCache:_booleanEnv("JITI_FS_CACHE",_booleanEnv("JITI_CACHE",!0)),rebuildFsCache:_booleanEnv("JITI_REBUILD_FS_CACHE",!1),moduleCache:_booleanEnv("JITI_MODULE_CACHE",_booleanEnv("JITI_REQUIRE_CACHE",!0)),debug:_booleanEnv("JITI_DEBUG",!1),sourceMaps:_booleanEnv("JITI_SOURCE_MAPS",!1),interopDefault:_booleanEnv("JITI_INTEROP_DEFAULT",!0),extensions:_jsonEnv("JITI_EXTENSIONS",[".js",".mjs",".cjs",".ts",".tsx",".mts",".cts",".mtsx",".ctsx"]),alias:_jsonEnv("JITI_ALIAS",{}),nativeModules:_jsonEnv("JITI_NATIVE_MODULES",[]),transformModules:_jsonEnv("JITI_TRANSFORM_MODULES",[]),tryNative:_jsonEnv("JITI_TRY_NATIVE","Bun"in globalThis),jsx:_booleanEnv("JITI_JSX",!1)};t.jsx&&t.extensions.push(".jsx",".tsx");let i={};return void 0!==e.cache&&(i.fsCache=e.cache),void 0!==e.requireCache&&(i.moduleCache=e.requireCache),{...t,...i,...e}}function _booleanEnv(e,t){return!!_jsonEnv(e,t)}function _jsonEnv(e,t){let i=process.env[e];if(!(e in process.env))return t;try{return JSON.parse(i)}catch{return t}}let tA=/\.(c|m)?j(sx?)$/,tN=/\.(c|m)?t(sx?)$/;function jitiResolve(e,t,i){let s,r;if(e.isNativeRe.test(t))return t;e.alias&&(t=resolveAlias(t,e.alias));let n=i?.parentURL||e.url;for(let a of(isDir(n)&&(n=pathe_M_eThtNZ_join(n,"_index.js")),(i?.async?[i?.conditions,["node","import"],["node","require"]]:[i?.conditions,["node","require"],["node","import"]]).filter(Boolean))){try{s=resolvePathSync(t,{url:n,conditions:a,extensions:e.opts.extensions})}catch(e){r=e}if(s)return s}try{return e.nativeRequire.resolve(t,{paths:i.paths})}catch(e){r=e}for(let r of e.additionalExts)if((s=tryNativeRequireResolve(e,t+r,n,i)||tryNativeRequireResolve(e,t+"/index"+r,n,i))||(tN.test(e.filename)||tN.test(e.parentModule?.filename||"")||tA.test(t))&&(s=tryNativeRequireResolve(e,t.replace(tA,".$1t$2"),n,i)))return s;if(!i?.try)throw r}function tryNativeRequireResolve(e,t,i,s){try{return e.nativeRequire.resolve(t,{...s,paths:[pathe_M_eThtNZ_dirname(fileURLToPath(i)),...s?.paths||[]]})}catch{}}let tL=require("node:perf_hooks"),tD=require("node:vm");var tO=__webpack_require__.n(tD);function jitiRequire(e,t,i){let s=e.parentCache||{};if(t.startsWith("node:"))return nativeImportOrRequire(e,t,i.async);if(t.startsWith("file:"))t=(0,eE.fileURLToPath)(t);else if(t.startsWith("data:")){if(!i.async)throw Error("`data:` URLs are only supported in ESM context. Use `import` or `jiti.import` instead.");return debug(e,"[native]","[data]","[import]",t),nativeImportOrRequire(e,t,!0)}if(ed.builtinModules.includes(t)||".pnp.js"===t)return nativeImportOrRequire(e,t,i.async);if(e.opts.tryNative&&!e.opts.transformOptions)try{if(!(t=jitiResolve(e,t,i))&&i.try)return;if(debug(e,"[try-native]",i.async&&e.nativeImport?"[import]":"[require]",t),i.async&&e.nativeImport)return e.nativeImport(t).then(i=>(!1===e.opts.moduleCache&&delete e.nativeRequire.cache[t],jitiInteropDefault(e,i)));{let i=e.nativeRequire(t);return!1===e.opts.moduleCache&&delete e.nativeRequire.cache[t],jitiInteropDefault(e,i)}}catch(i){debug(e,`[try-native] Using fallback for ${t} because of an error:`,i)}let r=jitiResolve(e,t,i);if(!r&&i.try)return;let n=extname(r);if(".json"===n){debug(e,"[json]",r);let t=e.nativeRequire(r);return!t||"default"in t||Object.defineProperty(t,"default",{value:t,enumerable:!1}),t}if(n&&!e.opts.extensions.includes(n))return debug(e,"[native]","[unknown]",i.async?"[import]":"[require]",r),nativeImportOrRequire(e,r,i.async);if(e.isNativeRe.test(r))return debug(e,"[native]",i.async?"[import]":"[require]",r),nativeImportOrRequire(e,r,i.async);if(s[r])return jitiInteropDefault(e,s[r]?.exports);if(e.opts.moduleCache){let t=e.nativeRequire.cache[r];if(t?.loaded)return jitiInteropDefault(e,t.exports)}return eval_evalModule(e,(0,ef.readFileSync)(r,"utf8"),{id:t,filename:r,ext:n,cache:s,async:i.async})}function nativeImportOrRequire(e,t,i){return i&&e.nativeImport?e.nativeImport(normalizeWindowsImportId(t)).then(t=>jitiInteropDefault(e,t)):jitiInteropDefault(e,e.nativeRequire(t))}function getCache(e,t,i){if(!e.opts.fsCache||!t.filename)return i();let s=` /* v9-${utils_hash(t.source,16)} */
`,r=`${basename(pathe_M_eThtNZ_dirname(t.filename))}-${utils_filename(t.filename)}`+(e.opts.sourceMaps?"+map":"")+(t.interopDefault?".i":"")+`.${utils_hash(t.filename)}`+(t.async?".mjs":".cjs");t.jsx&&t.filename.endsWith("x")&&(r+="x");let n=pathe_M_eThtNZ_join(e.opts.fsCache,r);if(!e.opts.rebuildFsCache&&(0,ef.existsSync)(n)){let i=(0,ef.readFileSync)(n,"utf8");if(i.endsWith(s))return debug(e,"[cache]","[hit]",t.filename,"~>",n),i}debug(e,"[cache]","[miss]",t.filename);let a=i();return a.includes("__JITI_ERROR__")||((0,ef.writeFileSync)(n,a+s,"utf8"),debug(e,"[cache]","[store]",t.filename,"~>",n)),a}function prepareCacheDir(e){if(!0===e.opts.fsCache&&(e.opts.fsCache=getCacheDir(e)),e.opts.fsCache)try{if((0,ef.mkdirSync)(e.opts.fsCache,{recursive:!0}),!isWritable(e.opts.fsCache))throw Error("directory is not writable!")}catch(t){debug(e,"Error creating cache directory at ",e.opts.fsCache,t),e.opts.fsCache=!1}}function getCacheDir(e){let t=e.filename&&pathe_M_eThtNZ_resolve(e.filename,"../node_modules");if(t&&(0,ef.existsSync)(t))return pathe_M_eThtNZ_join(t,".cache/jiti");let i=(0,r.tmpdir)();if(process.env.TMPDIR&&i===process.cwd()&&!process.env.JITI_RESPECT_TMPDIR_ENV){let e=process.env.TMPDIR;delete process.env.TMPDIR,i=(0,r.tmpdir)(),process.env.TMPDIR=e}return pathe_M_eThtNZ_join(i,"jiti")}function transform(e,t){let i=getCache(e,t,()=>{let i=e.opts.transform({...e.opts.transformOptions,babel:{...e.opts.sourceMaps?{sourceFileName:t.filename,sourceMaps:"inline"}:{},...e.opts.transformOptions?.babel},interopDefault:e.opts.interopDefault,...t});return i.error&&e.opts.debug&&debug(e,i.error),i.code});return i.startsWith("#!")&&(i="// "+i),i}function eval_evalModule(e,t,i={}){let s,r,n=i.id||(i.filename?basename(i.filename):`_jitiEval.${i.ext||(i.async?"mjs":"js")}`),a=i.filename||jitiResolve(e,n,{async:i.async}),o=i.ext||extname(a),h=i.cache||e.parentCache||{},c=/\.[cm]?tsx?$/.test(o),p=".mjs"===o||".js"===o&&readNearestPackageJSON(a)?.type==="module",l=".cjs"===o,u=i.forceTranspile??(!l&&!(p&&i.async)&&(c||p||e.isTransformRe.test(a)||hasESMSyntax(t))),d=tL.performance.now();if(u){t=transform(e,{filename:a,source:t,ts:c,async:i.async??!1,jsx:e.opts.jsx});let s=Math.round((tL.performance.now()-d)*1e3)/1e3;debug(e,"[transpile]",i.async?"[esm]":"[cjs]",a,`(${s}ms)`)}else{if(debug(e,"[native]",i.async?"[import]":"[require]",a),i.async)return Promise.resolve(nativeImportOrRequire(e,a,i.async)).catch(s=>(debug(e,"Native import error:",s),debug(e,"[fallback]",a),eval_evalModule(e,t,{...i,forceTranspile:!0})));try{return nativeImportOrRequire(e,a,i.async)}catch(s){debug(e,"Native require error:",s),debug(e,"[fallback]",a),t=transform(e,{filename:a,source:t,ts:c,async:i.async??!1,jsx:e.opts.jsx})}}let f=new ed.Module(a);f.filename=a,e.parentModule&&(f.parent=e.parentModule,Array.isArray(e.parentModule.children)&&!e.parentModule.children.includes(f)&&e.parentModule.children.push(f));let m=createJiti(a,e.opts,{parentModule:f,parentCache:h,nativeImport:e.nativeImport,onError:e.onError,createRequire:e.createRequire},!0);f.require=m,f.path=pathe_M_eThtNZ_dirname(a),f.paths=ed.Module._nodeModulePaths(f.path),h[a]=f,e.opts.moduleCache&&(e.nativeRequire.cache[a]=f);let g=wrapModule(t,{async:i.async});try{s=tO().runInThisContext(g,{filename:a,lineOffset:0,displayErrors:!1})}catch(t){"SyntaxError"===t.name&&i.async&&e.nativeImport?(debug(e,"[esm]","[import]","[fallback]",a),s=esmEval(g,e.nativeImport)):(e.opts.moduleCache&&delete e.nativeRequire.cache[a],e.onError(t))}try{r=s(f.exports,f.require,f,f.filename,pathe_M_eThtNZ_dirname(f.filename),m.import,m.esmResolve)}catch(t){e.opts.moduleCache&&delete e.nativeRequire.cache[a],e.onError(t)}function next(){if(f.exports&&f.exports.__JITI_ERROR__){let{filename:t,line:i,column:s,code:r,message:n}=f.exports.__JITI_ERROR__,a=`${t}:${i}:${s}`,o=Error(`${r}: ${n} 
 ${a}`);Error.captureStackTrace(o,jitiRequire),e.onError(o)}return f.loaded=!0,jitiInteropDefault(e,f.exports)}return i.async?Promise.resolve(r).then(next):next()}function esmEval(e,t){let i=`data:text/javascript;base64,${Buffer.from(`export default ${e}`).toString("base64")}`;return(...e)=>t(i).then(t=>t.default(...e))}let tV="win32"===(0,r.platform)();function createJiti(e,t={},i,s=!1){let r=s?t:resolveJitiOptions(t),n=r.alias&&Object.keys(r.alias).length>0?normalizeAliases(r.alias||{}):void 0,a=["typescript","jiti",...r.nativeModules||[]],o=RegExp(`node_modules/(${a.map(e=>escapeStringRegexp(e)).join("|")})/`),h=[...r.transformModules||[]],c=RegExp(`node_modules/(${h.map(e=>escapeStringRegexp(e)).join("|")})/`);e||(e=process.cwd()),!s&&isDir(e)&&(e=pathe_M_eThtNZ_join(e,"_index.js"));let p=pathToFileURL(e),l=[...r.extensions].filter(e=>".js"!==e),u=i.createRequire(tV?e.replace(/\//g,"\\"):e),d={filename:e,url:p,opts:r,alias:n,nativeModules:a,transformModules:h,isNativeRe:o,isTransformRe:c,additionalExts:l,nativeRequire:u,onError:i.onError,parentModule:i.parentModule,parentCache:i.parentCache,nativeImport:i.nativeImport,createRequire:i.createRequire};return s||debug(d,"[init]",...[["version:","2.6.0"],["module-cache:",r.moduleCache],["fs-cache:",r.fsCache],["rebuild-fs-cache:",r.rebuildFsCache],["interop-defaults:",r.interopDefault]].flat()),s||prepareCacheDir(d),Object.assign(function(e){return jitiRequire(d,e,{async:!1})},{cache:r.moduleCache?u.cache:Object.create(null),extensions:u.extensions,main:u.main,options:r,resolve:Object.assign(function(e){return jitiResolve(d,e,{async:!1})},{paths:u.resolve.paths}),transform:e=>transform(d,e),evalModule:(e,t)=>eval_evalModule(d,e,t),async import(e,t){let i=await jitiRequire(d,e,{...t,async:!0});return t?.default?i?.default??i:i},esmResolve(e,t){"string"==typeof t&&(t={parentURL:t});let i=jitiResolve(d,e,{parentURL:p,...t,async:!0});return!i||"string"!=typeof i||i.startsWith("file://")?i:pathToFileURL(i)}})}})(),module.exports=i.default})();